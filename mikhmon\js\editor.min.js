!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.CodeMirror=t()}(this,function(){"use strict";var e=navigator.userAgent,t=navigator.platform,m=/gecko\/\d/i.test(e),r=/MSIE \d/.test(e),n=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),i=/Edge\/(\d+)/.exec(e),x=r||n||i,k=x&&(r?document.documentMode||6:+(i||n)[1]),b=!i&&/WebKit\//.test(e),o=b&&/Qt\/\d+\.\d+/.test(e),a=!i&&/Chrome\//.test(e),g=/Opera\//.test(e),s=/Apple Computer/.test(navigator.vendor),l=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),c=/PhantomJS/.test(e),u=!i&&/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),d=/Android/.test(e),f=u||d||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),w=u||/Mac/.test(t),h=/\bCrOS\b/.test(e),p=/win/i.test(t),v=g&&e.match(/Version\/(\d*\.\d*)/);v&&(v=Number(v[1])),v&&15<=v&&(b=!(g=!1));var y=w&&(o||g&&(null==v||v<12.11)),_=m||x&&9<=k;function C(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var S,T=function(e,t){var r=e.className,n=C(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function L(e){for(var t=e.childNodes.length;0<t;--t)e.removeChild(e.firstChild);return e}function M(e,t){return L(e).appendChild(t)}function A(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function N(e,t,r,n){var i=A(e,t,r,n);return i.setAttribute("role","presentation"),i}function O(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function D(){var t;try{t=document.activeElement}catch(e){t=document.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function z(e,t){var r=e.className;C(t).test(r)||(e.className+=(r?" ":"")+t)}function F(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!C(r[n]).test(t)&&(t+=" "+r[n]);return t}S=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(e){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var I=function(e){e.select()};function P(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function E(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function q(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,a=i||0;;){var l=e.indexOf("\t",o);if(l<0||t<=l)return a+(t-o);a+=l-o,a+=r-a%r,o=l+1}}u?I=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:x&&(I=function(e){try{e.select()}catch(e){}});var W=function(){this.id=null};function H(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}W.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var R=30,B={toString:function(){return"CodeMirror.Pass"}},j={scroll:!1},U={origin:"*mouse"},K={origin:"+move"};function V(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var a=o-n;if(o==e.length||t<=i+a)return n+Math.min(a,t-i);if(i+=o-n,n=o+1,t<=(i+=r-i%r))return n}}var $=[""];function G(e){for(;$.length<=e;)$.push(X($)+" ");return $[e]}function X(e){return e[e.length-1]}function Y(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function Z(){}function Q(e,t){var r;return Object.create?r=Object.create(e):(Z.prototype=e,r=new Z),t&&E(t,r),r}var J=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ee(e){return/\w/.test(e)||""<e&&(e.toUpperCase()!=e.toLowerCase()||J.test(e))}function te(e,t){return t?!!(-1<t.source.indexOf("\\w")&&ee(e))||t.test(e):ee(e)}function re(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ne=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ie(e){return 768<=e.charCodeAt(0)&&ne.test(e)}function oe(e,t,r){for(;(r<0?0<t:t<e.length)&&ie(e.charAt(t));)t+=r;return t}function ae(e,t,r){for(var n=r<t?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}function le(e,t,r){var n=this;this.input=r,n.scrollbarFiller=A("div",null,"CodeMirror-scrollbar-filler"),n.scrollbarFiller.setAttribute("cm-not-content","true"),n.gutterFiller=A("div",null,"CodeMirror-gutter-filler"),n.gutterFiller.setAttribute("cm-not-content","true"),n.lineDiv=N("div",null,"CodeMirror-code"),n.selectionDiv=A("div",null,null,"position: relative; z-index: 1"),n.cursorDiv=A("div",null,"CodeMirror-cursors"),n.measure=A("div",null,"CodeMirror-measure"),n.lineMeasure=A("div",null,"CodeMirror-measure"),n.lineSpace=N("div",[n.measure,n.lineMeasure,n.selectionDiv,n.cursorDiv,n.lineDiv],null,"position: relative; outline: none");var i=N("div",[n.lineSpace],"CodeMirror-lines");n.mover=A("div",[i],null,"position: relative"),n.sizer=A("div",[n.mover],"CodeMirror-sizer"),n.sizerWidth=null,n.heightForcer=A("div",null,null,"position: absolute; height: "+R+"px; width: 1px;"),n.gutters=A("div",null,"CodeMirror-gutters"),n.lineGutter=null,n.scroller=A("div",[n.sizer,n.heightForcer,n.gutters],"CodeMirror-scroll"),n.scroller.setAttribute("tabIndex","-1"),n.wrapper=A("div",[n.scrollbarFiller,n.gutterFiller,n.scroller],"CodeMirror"),x&&k<8&&(n.gutters.style.zIndex=-1,n.scroller.style.paddingRight=0),b||m&&f||(n.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(n.wrapper):e(n.wrapper)),n.viewFrom=n.viewTo=t.first,n.reportedViewFrom=n.reportedViewTo=t.first,n.view=[],n.renderedView=null,n.externalMeasured=null,n.viewOffset=0,n.lastWrapHeight=n.lastWrapWidth=0,n.updateLineNumbers=null,n.nativeBarWidth=n.barHeight=n.barWidth=0,n.scrollbarsClipped=!1,n.lineNumWidth=n.lineNumInnerWidth=n.lineNumChars=null,n.alignWidgets=!1,n.cachedCharWidth=n.cachedTextHeight=n.cachedPaddingH=null,n.maxLine=null,n.maxLineLength=0,n.maxLineChanged=!1,n.wheelDX=n.wheelDY=n.wheelStartX=n.wheelStartY=null,n.shift=!1,n.selForContextMenu=null,n.activeTouch=null,r.init(n)}function se(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function ce(e,r,n){var i=[],o=r.line;return e.iter(r.line,n.line+1,function(e){var t=e.text;o==n.line&&(t=t.slice(0,n.ch)),o==r.line&&(t=t.slice(r.ch)),i.push(t),++o}),i}function ue(e,t,r){var n=[];return e.iter(t,r,function(e){n.push(e.text)}),n}function de(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function fe(e){if(null==e.parent)return null;for(var t=e.parent,r=H(t.lines,e),n=t.parent;n;n=(t=n).parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function he(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var a=0;a<e.lines.length;++a){var l=e.lines[a].height;if(t<l)break;t-=l}return r+a}function pe(e,t){return t>=e.first&&t<e.first+e.size}function me(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function ge(e,t,r){if(void 0===r&&(r=null),!(this instanceof ge))return new ge(e,t,r);this.line=e,this.ch=t,this.sticky=r}function ve(e,t){return e.line-t.line||e.ch-t.ch}function ye(e,t){return e.sticky==t.sticky&&0==ve(e,t)}function be(e){return ge(e.line,e.ch)}function we(e,t){return ve(e,t)<0?t:e}function xe(e,t){return ve(e,t)<0?e:t}function ke(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function _e(e,t){if(t.line<e.first)return ge(e.first,0);var r,n,i,o=e.first+e.size-1;return t.line>o?ge(o,se(e,o).text.length):(n=se(e,(r=t).line).text.length,null==(i=r.ch)||n<i?ge(r.line,n):i<0?ge(r.line,0):r)}function Ce(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=_e(e,t[n]);return r}var Se=!1,Te=!1;function Le(e,t,r){this.marker=e,this.from=t,this.to=r}function Me(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function Ne(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Ae(e,t){if(t.full)return null;var r=pe(e,t.from.line)&&se(e,t.from.line).markedSpans,n=pe(e,t.to.line)&&se(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,a=0==ve(t.from,t.to),l=function(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==a.type&&(!r||!o.marker.insertLeft)){var l=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new Le(a,o.from,l?null:o.to))}}return n}(r,i,a),s=function(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.to||(a.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==a.type&&(!r||o.marker.insertLeft)){var l=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new Le(a,l?null:o.from-t,null==o.to?null:o.to-t))}}return n}(n,o,a),c=1==t.text.length,u=X(t.text).length+(c?i:0);if(l)for(var d=0;d<l.length;++d){var f=l[d];if(null==f.to){var h=Me(s,f.marker);h?c&&(f.to=null==h.to?null:h.to+u):f.to=i}}if(s)for(var p=0;p<s.length;++p){var m=s[p];if(null!=m.to&&(m.to+=u),null==m.from)Me(l,m.marker)||(m.from=u,c&&(l||(l=[])).push(m));else m.from+=u,c&&(l||(l=[])).push(m)}l&&(l=Oe(l)),s&&s!=l&&(s=Oe(s));var g=[l];if(!c){var v,y=t.text.length-2;if(0<y&&l)for(var b=0;b<l.length;++b)null==l[b].to&&(v||(v=[])).push(new Le(l[b].marker,null,null));for(var w=0;w<y;++w)g.push(v);g.push(s)}return g}function Oe(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function De(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function ze(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function Fe(e){return e.inclusiveLeft?-1:0}function Ie(e){return e.inclusiveRight?1:0}function Pe(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=ve(n.from,i.from)||Fe(e)-Fe(t);if(o)return-o;var a=ve(n.to,i.to)||Ie(e)-Ie(t);return a||t.id-e.id}function Ee(e,t){var r,n=Te&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||Pe(r,i.marker)<0)&&(r=i.marker);return r}function qe(e){return Ee(e,!0)}function We(e){return Ee(e,!1)}function He(e,t){var r,n=Te&&e.markedSpans;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!r||Pe(r,o.marker)<0)&&(r=o.marker)}return r}function Re(e,t,r,n,i){var o=se(e,t),a=Te&&o.markedSpans;if(a)for(var l=0;l<a.length;++l){var s=a[l];if(s.marker.collapsed){var c=s.marker.find(0),u=ve(c.from,r)||Fe(s.marker)-Fe(i),d=ve(c.to,n)||Ie(s.marker)-Ie(i);if(!(0<=u&&d<=0||u<=0&&0<=d)&&(u<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?0<=ve(c.to,r):0<ve(c.to,r))||0<=u&&(s.marker.inclusiveRight&&i.inclusiveLeft?ve(c.from,n)<=0:ve(c.from,n)<0)))return!0}}}function Be(e){for(var t;t=qe(e);)e=t.find(-1,!0).line;return e}function je(e,t){var r=se(e,t),n=Be(r);return r==n?t:fe(n)}function Ue(e,t){if(t>e.lastLine())return t;var r,n=se(e,t);if(!Ke(e,n))return t;for(;r=We(n);)n=r.find(1,!0).line;return fe(n)+1}function Ke(e,t){var r=Te&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&Ve(e,t,n))return!0}}function Ve(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return Ve(e,n.line,Me(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&Ve(e,t,i))return!0}function $e(e){for(var t=0,r=(e=Be(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var a=0;a<o.children.length;++a){var l=o.children[a];if(l==r)break;t+=l.height}return t}function Ge(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=qe(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=We(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function Xe(e){var r=e.display,t=e.doc;r.maxLine=se(t,t.first),r.maxLineLength=Ge(r.maxLine),r.maxLineChanged=!0,t.iter(function(e){var t=Ge(e);t>r.maxLineLength&&(r.maxLineLength=t,r.maxLine=e)})}var Ye=null;function Ze(e,t,r){var n;Ye=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:Ye=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:Ye=i)}return null!=n?n:Ye}var Qe=function(){var E="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",q="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";var W=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,H=/[stwN]/,R=/[LRr]/,B=/[Lb1n]/,j=/[1n]/;function U(e,t,r){this.level=e,this.from=t,this.to=r}return function(e,t){var r="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!W.test(e))return!1;for(var n,i=e.length,o=[],a=0;a<i;++a)o.push((n=e.charCodeAt(a))<=247?E.charAt(n):1424<=n&&n<=1524?"R":1536<=n&&n<=1785?q.charAt(n-1536):1774<=n&&n<=2220?"r":8192<=n&&n<=8203?"w":8204==n?"b":"L");for(var l=0,s=r;l<i;++l){var c=o[l];"m"==c?o[l]=s:s=c}for(var u=0,d=r;u<i;++u){var f=o[u];"1"==f&&"r"==d?o[u]="n":R.test(f)&&"r"==(d=f)&&(o[u]="R")}for(var h=1,p=o[0];h<i-1;++h){var m=o[h];"+"==m&&"1"==p&&"1"==o[h+1]?o[h]="1":","!=m||p!=o[h+1]||"1"!=p&&"n"!=p||(o[h]=p),p=m}for(var g=0;g<i;++g){var v=o[g];if(","==v)o[g]="N";else if("%"==v){var y=void 0;for(y=g+1;y<i&&"%"==o[y];++y);for(var b=g&&"!"==o[g-1]||y<i&&"1"==o[y]?"1":"N",w=g;w<y;++w)o[w]=b;g=y-1}}for(var x=0,k=r;x<i;++x){var _=o[x];"L"==k&&"1"==_?o[x]="L":R.test(_)&&(k=_)}for(var C=0;C<i;++C)if(H.test(o[C])){var S=void 0;for(S=C+1;S<i&&H.test(o[S]);++S);for(var T="L"==(C?o[C-1]:r),L=T==("L"==(S<i?o[S]:r))?T?"L":"R":r,M=C;M<S;++M)o[M]=L;C=S-1}for(var N,A=[],O=0;O<i;)if(B.test(o[O])){var D=O;for(++O;O<i&&B.test(o[O]);++O);A.push(new U(0,D,O))}else{var z=O,F=A.length;for(++O;O<i&&"L"!=o[O];++O);for(var I=z;I<O;)if(j.test(o[I])){z<I&&A.splice(F,0,new U(1,z,I));var P=I;for(++I;I<O&&j.test(o[I]);++I);A.splice(F,0,new U(2,P,I)),z=I}else++I;z<O&&A.splice(F,0,new U(1,z,O))}return"ltr"==t&&(1==A[0].level&&(N=e.match(/^\s+/))&&(A[0].from=N[0].length,A.unshift(new U(0,0,N[0].length))),1==X(A).level&&(N=e.match(/\s+$/))&&(X(A).to-=N[0].length,A.push(new U(0,i-N[0].length,i)))),"rtl"==t?A.reverse():A}}();function Je(e,t){var r=e.order;return null==r&&(r=e.order=Qe(e.text,t)),r}var et=[],tt=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||et).concat(r)}};function rt(e,t){return e._handlers&&e._handlers[t]||et}function nt(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=H(i,r);-1<o&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function it(e,t){var r=rt(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function ot(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),it(e,r||t.type,e,t),dt(t)||t.codemirrorIgnore}function at(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==H(r,t[n])&&r.push(t[n])}function lt(e,t){return 0<rt(e,t).length}function st(e){e.prototype.on=function(e,t){tt(this,e,t)},e.prototype.off=function(e,t){nt(this,e,t)}}function ct(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function ut(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function dt(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function ft(e){ct(e),ut(e)}function ht(e){return e.target||e.srcElement}function pt(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),w&&e.ctrlKey&&1==t&&(t=3),t}var mt,gt,vt=function(){if(x&&k<9)return!1;var e=A("div");return"draggable"in e||"dragDrop"in e}();function yt(e){if(null==mt){var t=A("span","​");M(e,A("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(mt=t.offsetWidth<=1&&2<t.offsetHeight&&!(x&&k<8))}var r=mt?A("span","​"):A("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function bt(e){if(null!=gt)return gt;var t=M(e,document.createTextNode("AخA")),r=S(t,0,1).getBoundingClientRect(),n=S(t,1,2).getBoundingClientRect();return L(e),!(!r||r.left==r.right)&&(gt=n.right-r.right<3)}var wt,xt=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");-1!=a?(r.push(o.slice(0,a)),t+=a+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},kt=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},_t="oncopy"in(wt=A("div"))||(wt.setAttribute("oncopy","return;"),"function"==typeof wt.oncopy),Ct=null;var St={},Tt={};function Lt(e){if("string"==typeof e&&Tt.hasOwnProperty(e))e=Tt[e];else if(e&&"string"==typeof e.name&&Tt.hasOwnProperty(e.name)){var t=Tt[e.name];"string"==typeof t&&(t={name:t}),(e=Q(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Lt("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Lt("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Mt(e,t){t=Lt(t);var r=St[t.name];if(!r)return Mt(e,"text/plain");var n=r(e,t);if(Nt.hasOwnProperty(t.name)){var i=Nt[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)n[a]=t.modeProps[a];return n}var Nt={};function At(e,t){E(t,Nt.hasOwnProperty(e)?Nt[e]:Nt[e]={})}function Ot(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function Dt(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function zt(e,t,r){return!e.startState||e.startState(t,r)}var Ft=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};Ft.prototype.eol=function(){return this.pos>=this.string.length},Ft.prototype.sol=function(){return this.pos==this.lineStart},Ft.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Ft.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Ft.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},Ft.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},Ft.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Ft.prototype.skipToEnd=function(){this.pos=this.string.length},Ft.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(-1<t)return this.pos=t,!0},Ft.prototype.backUp=function(e){this.pos-=e},Ft.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=q(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?q(this.string,this.lineStart,this.tabSize):0)},Ft.prototype.indentation=function(){return q(this.string,null,this.tabSize)-(this.lineStart?q(this.string,this.lineStart,this.tabSize):0)},Ft.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&0<n.index?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},Ft.prototype.current=function(){return this.string.slice(this.start,this.pos)},Ft.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},Ft.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},Ft.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var It=function(e,t){this.state=e,this.lookAhead=t},Pt=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};function Et(t,r,n,e){var s=[t.state.modeGen],i={};Vt(t,r.text,t.doc.mode,n,function(e,t){return s.push(e,t)},i,e);for(var c=n.state,o=function(e){n.baseTokens=s;var o=t.state.overlays[e],a=1,l=0;n.state=!0,Vt(t,r.text,o.mode,n,function(e,t){for(var r=a;l<e;){var n=s[a];e<n&&s.splice(a,1,e,s[a+1],n),a+=2,l=Math.min(e,n)}if(t)if(o.opaque)s.splice(r,a-r,e,"overlay "+t),a=r+2;else for(;r<a;r+=2){var i=s[r+1];s[r+1]=(i?i+" ":"")+"overlay "+t}},i),n.state=c,n.baseTokens=null,n.baseTokenPos=1},a=0;a<t.state.overlays.length;++a)o(a);return{styles:s,classes:i.bgClass||i.textClass?i:null}}function qt(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=Wt(e,fe(t)),i=t.text.length>e.options.maxHighlightLength&&Ot(e.doc.mode,n.state),o=Et(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function Wt(r,n,e){var t=r.doc,i=r.display;if(!t.mode.startState)return new Pt(t,!0,n);var o=function(e,t,r){for(var n,i,o=e.doc,a=r?-1:t-(e.doc.mode.innerMode?1e3:100),l=t;a<l;--l){if(l<=o.first)return o.first;var s=se(o,l-1),c=s.stateAfter;if(c&&(!r||l+(c instanceof It?c.lookAhead:0)<=o.modeFrontier))return l;var u=q(s.text,null,e.options.tabSize);(null==i||u<n)&&(i=l-1,n=u)}return i}(r,n,e),a=o>t.first&&se(t,o-1).stateAfter,l=a?Pt.fromSaved(t,a,o):new Pt(t,zt(t.mode),o);return t.iter(o,n,function(e){Ht(r,e.text,l);var t=l.line;e.stateAfter=t==n-1||t%5==0||t>=i.viewFrom&&t<i.viewTo?l.save():null,l.nextLine()}),e&&(t.modeFrontier=l.line),l}function Ht(e,t,r,n){var i=e.doc.mode,o=new Ft(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&Rt(i,r.state);!o.eol();)Bt(i,o,r.state),o.start=o.pos}function Rt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=Dt(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function Bt(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=Dt(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}Pt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},Pt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},Pt.prototype.nextLine=function(){this.line++,0<this.maxLookAhead&&this.maxLookAhead--},Pt.fromSaved=function(e,t,r){return t instanceof It?new Pt(e,Ot(e.mode,t.state),r,t.lookAhead):new Pt(e,Ot(e.mode,t),r)},Pt.prototype.save=function(e){var t=!1!==e?Ot(this.doc.mode,this.state):this.state;return 0<this.maxLookAhead?new It(t,this.maxLookAhead):t};var jt=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r};function Ut(e,t,r,n){var i,o,a=e.doc,l=a.mode,s=se(a,(t=_e(a,t)).line),c=Wt(e,t.line,r),u=new Ft(s.text,e.options.tabSize,c);for(n&&(o=[]);(n||u.pos<t.ch)&&!u.eol();)u.start=u.pos,i=Bt(l,u,c.state),n&&o.push(new jt(u,i,Ot(a.mode,c.state)));return n?o:new jt(u,i,c.state)}function Kt(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|s)"+r[2]+"(?:$|s)").test(t[n])||(t[n]+=" "+r[2])}return e}function Vt(e,t,r,n,i,o,a){var l=r.flattenSpans;null==l&&(l=e.options.flattenSpans);var s,c=0,u=null,d=new Ft(t,e.options.tabSize,n),f=e.options.addModeClass&&[null];for(""==t&&Kt(Rt(r,n.state),o);!d.eol();){if(d.pos>e.options.maxHighlightLength?(l=!1,a&&Ht(e,t,n,d.pos),d.pos=t.length,s=null):s=Kt(Bt(r,d,n.state,f),o),f){var h=f[0].name;h&&(s="m-"+(s?h+" "+s:h))}if(!l||u!=s){for(;c<d.start;)i(c=Math.min(d.start,c+5e3),u);u=s}d.start=d.pos}for(;c<d.pos;){var p=Math.min(d.pos,c+5e3);i(p,u),c=p}}var $t=function(e,t,r){this.text=e,ze(this,t),this.height=r?r(this):1};$t.prototype.lineNo=function(){return fe(this)},st($t);var Gt={},Xt={};function Yt(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?Xt:Gt;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function Zt(e,t){var r=N("span",null,null,b?"padding-right: .1px":null),n={pre:N("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;n.pos=0,n.addToken=Jt,bt(e.display.measure)&&(a=Je(o,e.doc.direction))&&(n.addToken=er(n.addToken,a)),n.map=[],rr(o,n,qt(e,o,t!=e.display.externalMeasured&&fe(o))),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=F(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=F(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(yt(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(b){var l=n.content.lastChild;(/\bcm-tab\b/.test(l.className)||l.querySelector&&l.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return it(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=F(n.pre.className,n.textClass||"")),n}function Qt(e){var t=A("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Jt(e,t,r,n,i,o,a){if(t){var l,s=e.splitSpaces?function(e,t){if(1<e.length&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}(t,e.trailingSpace):t,c=e.cm.state.specialChars,u=!1;if(c.test(t)){l=document.createDocumentFragment();for(var d=0;;){c.lastIndex=d;var f=c.exec(t),h=f?f.index-d:t.length-d;if(h){var p=document.createTextNode(s.slice(d,d+h));x&&k<9?l.appendChild(A("span",[p])):l.appendChild(p),e.map.push(e.pos,e.pos+h,p),e.col+=h,e.pos+=h}if(!f)break;d+=h+1;var m=void 0;if("\t"==f[0]){var g=e.cm.options.tabSize,v=g-e.col%g;(m=l.appendChild(A("span",G(v),"cm-tab"))).setAttribute("role","presentation"),m.setAttribute("cm-text","\t"),e.col+=v}else"\r"==f[0]||"\n"==f[0]?(m=l.appendChild(A("span","\r"==f[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",f[0]):((m=e.cm.options.specialCharPlaceholder(f[0])).setAttribute("cm-text",f[0]),x&&k<9?l.appendChild(A("span",[m])):l.appendChild(m)),e.col+=1;e.map.push(e.pos,e.pos+1,m),e.pos++}}else e.col+=t.length,l=document.createTextNode(s),e.map.push(e.pos,e.pos+t.length,l),x&&k<9&&(u=!0),e.pos+=t.length;if(e.trailingSpace=32==s.charCodeAt(t.length-1),r||n||i||u||o){var y=r||"";n&&(y+=n),i&&(y+=i);var b=A("span",[l],y,o);if(a)for(var w in a)a.hasOwnProperty(w)&&"style"!=w&&"class"!=w&&b.setAttribute(w,a[w]);return e.content.appendChild(b)}e.content.appendChild(l)}}function er(d,f){return function(e,t,r,n,i,o,a){r=r?r+" cm-force-border":"cm-force-border";for(var l=e.pos,s=l+t.length;;){for(var c=void 0,u=0;u<f.length&&!((c=f[u]).to>l&&c.from<=l);u++);if(c.to>=s)return d(e,t,r,n,i,o,a);d(e,t.slice(0,c.to-l),r,n,null,o,a),n=null,t=t.slice(c.to-l),l=c.to}}}function tr(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function rr(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var a,l,s,c,u,d,f,h=i.length,p=0,m=1,g="",v=0;;){if(v==p){s=c=u=l="",d=f=null,v=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],k=x.marker;if("bookmark"==k.type&&x.from==p&&k.widgetNode)y.push(k);else if(x.from<=p&&(null==x.to||x.to>p||k.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&v>x.to&&(v=x.to,c=""),k.className&&(s+=" "+k.className),k.css&&(l=(l?l+";":"")+k.css),k.startStyle&&x.from==p&&(u+=" "+k.startStyle),k.endStyle&&x.to==v&&(b||(b=[])).push(k.endStyle,x.to),k.title&&((f||(f={})).title=k.title),k.attributes)for(var _ in k.attributes)(f||(f={}))[_]=k.attributes[_];k.collapsed&&(!d||Pe(d.marker,k)<0)&&(d=x)}else x.from>p&&v>x.from&&(v=x.from)}if(b)for(var C=0;C<b.length;C+=2)b[C+1]==v&&(c+=" "+b[C]);if(!d||d.from==p)for(var S=0;S<y.length;++S)tr(t,0,y[S]);if(d&&(d.from||0)==p){if(tr(t,(null==d.to?h+1:d.to)-p,d.marker,null==d.from),null==d.to)return;d.to==p&&(d=!1)}}if(h<=p)break;for(var T=Math.min(h,v);;){if(g){var L=p+g.length;if(!d){var M=T<L?g.slice(0,T-p):g;t.addToken(t,M,a?a+s:s,u,p+M.length==v?c:"",l,f)}if(T<=L){g=g.slice(T-p),p=T;break}p=L,u=""}g=i.slice(o,o=r[m++]),a=Yt(r[m++],t.cm.options)}}else for(var N=1;N<r.length;N+=2)t.addToken(t,i.slice(o,o=r[N]),Yt(r[N+1],t.cm.options))}function nr(e,t,r){this.line=t,this.rest=function(e){for(var t,r;t=We(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}(t),this.size=this.rest?fe(X(this.rest))-r+1:1,this.node=this.text=null,this.hidden=Ke(e,t)}function ir(e,t,r){for(var n,i=[],o=t;o<r;o=n){var a=new nr(e.doc,se(e.doc,o),o);n=o+a.size,i.push(a)}return i}var or=null;var ar=null;function lr(e,t){var r=rt(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);or?n=or.delayedCallbacks:ar?n=ar:(n=ar=[],setTimeout(sr,0));for(var o=function(e){n.push(function(){return r[e].apply(null,i)})},a=0;a<r.length;++a)o(a)}}function sr(){var e=ar;ar=null;for(var t=0;t<e.length;++t)e[t]()}function cr(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?fr(e,t):"gutter"==o?pr(e,t,r,n):"class"==o?hr(e,t):"widget"==o&&mr(e,t,n)}t.changes=null}function ur(e){return e.node==e.text&&(e.node=A("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),x&&k<8&&(e.node.style.zIndex=2)),e.node}function dr(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):Zt(e,t)}function fr(e,t){var r=t.text.className,n=dr(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,hr(e,t)):r&&(t.text.className=r)}function hr(e,t){!function(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var n=ur(t);t.background=n.insertBefore(A("div",null,r),n.firstChild),e.display.input.setUneditable(t.background)}}(e,t),t.line.wrapClass?ur(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function pr(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=ur(t);t.gutterBackground=A("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var a=ur(t),l=t.gutter=A("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(e.display.input.setUneditable(l),a.insertBefore(l,t.text),t.line.gutterClass&&(l.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=l.appendChild(A("div",me(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.options.gutters.length;++s){var c=e.options.gutters[s],u=o.hasOwnProperty(c)&&o[c];u&&l.appendChild(A("div",[u],"CodeMirror-gutter-elt","left: "+n.gutterLeft[c]+"px; width: "+n.gutterWidth[c]+"px"))}}}function mr(e,t,r){t.alignable&&(t.alignable=null);for(var n=t.node.firstChild,i=void 0;n;n=i)i=n.nextSibling,"CodeMirror-linewidget"==n.className&&t.node.removeChild(n);gr(e,t,r)}function gr(e,t,r){if(vr(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)vr(e,t.rest[n],t,r,!1)}function vr(e,t,r,n,i){if(t.widgets)for(var o=ur(r),a=0,l=t.widgets;a<l.length;++a){var s=l[a],c=A("div",[s.node],"CodeMirror-linewidget");s.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),yr(s,c,r,n),e.display.input.setUneditable(c),i&&s.above?o.insertBefore(c,r.gutter||r.text):o.appendChild(c),lr(s,"redraw")}}function yr(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function br(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!O(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),M(t.display.measure,A("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function wr(e,t){for(var r=ht(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function xr(e){return e.lineSpace.offsetTop}function kr(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function _r(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=M(e.measure,A("pre","x")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function Cr(e){return R-e.display.nativeBarWidth}function Sr(e){return e.display.scroller.clientWidth-Cr(e)-e.display.barWidth}function Tr(e){return e.display.scroller.clientHeight-Cr(e)-e.display.barHeight}function Lr(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(fe(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Mr(e,t,r,n){return Or(e,Ar(e,t),r,n)}function Nr(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[ln(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function Ar(e,t){var r=fe(t),n=Nr(e,r);n&&!n.text?n=null:n&&n.changes&&(cr(e,n,r,tn(e)),e.curOp.forceUpdate=!0),n||(n=function(e,t){var r=fe(t=Be(t)),n=e.display.externalMeasured=new nr(e.doc,t,r);n.lineN=r;var i=n.built=Zt(e,n);return n.text=i.pre,M(e.display.lineMeasure,i.pre),n}(e,t));var i=Lr(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Or(e,t,r,n,i){t.before&&(r=-1);var o,a=r+(n||"");return t.cache.hasOwnProperty(a)?o=t.cache[a]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(!function(e,t,r){var n=e.options.lineWrapping,i=n&&Sr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),l=0;l<a.length-1;l++){var s=a[l],c=a[l+1];2<Math.abs(s.bottom-c.bottom)&&o.push((s.bottom+c.top)/2-r.top)}}o.push(r.bottom-r.top)}}(e,t.view,t.rect),t.hasHeights=!0),(o=function(e,t,r,n){var i,o=Fr(t.map,r,n),a=o.node,l=o.start,s=o.end,c=o.collapse;if(3==a.nodeType){for(var u=0;u<4;u++){for(;l&&ie(t.line.text.charAt(o.coverStart+l));)--l;for(;o.coverStart+s<o.coverEnd&&ie(t.line.text.charAt(o.coverStart+s));)++s;if((i=x&&k<9&&0==l&&s==o.coverEnd-o.coverStart?a.parentNode.getBoundingClientRect():Ir(S(a,l,s).getClientRects(),n)).left||i.right||0==l)break;s=l,l-=1,c="right"}x&&k<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=Ct)return Ct;var t=M(e,A("span","x")),r=t.getBoundingClientRect(),n=S(t,0,1).getBoundingClientRect();return Ct=1<Math.abs(r.left-n.left)}(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}(e.display.measure,i))}else{var d;0<l&&(c=n="right"),i=e.options.lineWrapping&&1<(d=a.getClientRects()).length?d["right"==n?d.length-1:0]:a.getBoundingClientRect()}if(x&&k<9&&!l&&(!i||!i.left&&!i.right)){var f=a.parentNode.getClientRects()[0];i=f?{left:f.left,right:f.left+en(e.display),top:f.top,bottom:f.bottom}:zr}for(var h=i.top-t.rect.top,p=i.bottom-t.rect.top,m=(h+p)/2,g=t.view.measure.heights,v=0;v<g.length-1&&!(m<g[v]);v++);var y=v?g[v-1]:0,b=g[v],w={left:("right"==c?i.right:i.left)-t.rect.left,right:("left"==c?i.left:i.right)-t.rect.left,top:y,bottom:b};i.left||i.right||(w.bogus=!0);e.options.singleCursorHeightPerLine||(w.rtop=h,w.rbottom=p);return w}(e,t,r,n)).bogus||(t.cache[a]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var Dr,zr={left:0,right:0,top:0,bottom:0};function Fr(e,t,r){for(var n,i,o,a,l,s,c=0;c<e.length;c+=3)if(l=e[c],s=e[c+1],t<l?(i=0,o=1,a="left"):t<s?o=(i=t-l)+1:(c==e.length-3||t==s&&e[c+3]>t)&&(i=(o=s-l)-1,s<=t&&(a="right")),null!=i){if(n=e[c+2],l==s&&r==(n.insertLeft?"left":"right")&&(a=r),"left"==r&&0==i)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)n=e[2+(c-=3)],a="left";if("right"==r&&i==s-l)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)n=e[(c+=3)+2],a="right";break}return{node:n,start:i,end:o,collapse:a,coverStart:l,coverEnd:s}}function Ir(e,t){var r=zr;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;0<=i&&(r=e[i]).left==r.right;i--);return r}function Pr(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Er(e){e.display.externalMeasure=null,L(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Pr(e.display.view[t])}function qr(e){Er(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Wr(){return a&&d?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function Hr(){return a&&d?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function Rr(e){var t=0;if(e.widgets)for(var r=0;r<e.widgets.length;++r)e.widgets[r].above&&(t+=br(e.widgets[r]));return t}function Br(e,t,r,n,i){if(!i){var o=Rr(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var a=$e(t);if("local"==n?a+=xr(e.display):a-=e.display.viewOffset,"page"==n||"window"==n){var l=e.display.lineSpace.getBoundingClientRect();a+=l.top+("window"==n?0:Hr());var s=l.left+("window"==n?0:Wr());r.left+=s,r.right+=s}return r.top+=a,r.bottom+=a,r}function jr(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=Wr(),i-=Hr();else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:n-a.left,top:i-a.top}}function Ur(e,t,r,n,i){return n||(n=se(e.doc,t.line)),Br(e,n,Mr(e,n,t.ch,i),r)}function Kr(n,e,i,o,a,l){function s(e,t){var r=Or(n,a,e,t?"right":"left",l);return t?r.left=r.right:r.right=r.left,Br(n,o,r,i)}o=o||se(n.doc,e.line),a||(a=Ar(n,o));var c=Je(o,n.doc.direction),t=e.ch,r=e.sticky;if(t>=o.text.length?(t=o.text.length,r="before"):t<=0&&(t=0,r="after"),!c)return s("before"==r?t-1:t,"before"==r);function u(e,t,r){return s(r?e-1:e,1==c[t].level!=r)}var d=Ze(c,t,r),f=Ye,h=u(t,d,"before"==r);return null!=f&&(h.other=u(t,f,"before"!=r)),h}function Vr(e,t){var r=0;t=_e(e.doc,t),e.options.lineWrapping||(r=en(e.display)*t.ch);var n=se(e.doc,t.line),i=$e(n)+xr(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function $r(e,t,r,n,i){var o=ge(e,t,r);return o.xRel=i,n&&(o.outside=!0),o}function Gr(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return $r(n.first,0,null,!0,-1);var i=he(n,r),o=n.first+n.size-1;if(o<i)return $r(n.first+n.size-1,se(n,o).text.length,null,!0,1);t<0&&(t=0);for(var a=se(n,i);;){var l=Qr(e,a,i,t,r),s=He(a,l.ch+(0<l.xRel?1:0));if(!s)return l;var c=s.find(1);if(c.line==i)return c;a=se(n,i=c.line)}}function Xr(t,e,r,n){n-=Rr(e);var i=e.text.length,o=ae(function(e){return Or(t,r,e-1).bottom<=n},i,0);return{begin:o,end:i=ae(function(e){return Or(t,r,e).top>n},o,i)}}function Yr(e,t,r,n){return r||(r=Ar(e,t)),Xr(e,t,r,Br(e,t,Or(e,r,n),"line").top)}function Zr(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function Qr(r,e,t,n,i){i-=$e(e);var o=Ar(r,e),a=Rr(e),l=0,s=e.text.length,c=!0,u=Je(e,r.doc.direction);if(u){var d=(r.options.lineWrapping?function(e,t,r,n,i,o,a){var l=Xr(e,t,n,a),s=l.begin,c=l.end;/\s/.test(t.text.charAt(c-1))&&c--;for(var u=null,d=null,f=0;f<i.length;f++){var h=i[f];if(!(h.from>=c||h.to<=s)){var p=1!=h.level,m=Or(e,n,p?Math.min(c,h.to)-1:Math.max(s,h.from)).right,g=m<o?o-m+1e9:m-o;(!u||g<d)&&(u=h,d=g)}}u||(u=i[i.length-1]);u.from<s&&(u={from:s,to:u.to,level:u.level});u.to>c&&(u={from:u.from,to:c,level:u.level});return u}:function(n,i,o,a,l,s,c){var e=ae(function(e){var t=l[e],r=1!=t.level;return Zr(Kr(n,ge(o,r?t.to:t.from,r?"before":"after"),"line",i,a),s,c,!0)},0,l.length-1),t=l[e];if(0<e){var r=1!=t.level,u=Kr(n,ge(o,r?t.from:t.to,r?"after":"before"),"line",i,a);Zr(u,s,c,!0)&&u.top>c&&(t=l[e-1])}return t})(r,e,t,o,u,n,i);l=(c=1!=d.level)?d.from:d.to-1,s=c?d.to:d.from-1}var f,h,p=null,m=null,g=ae(function(e){var t=Or(r,o,e);return t.top+=a,t.bottom+=a,!!Zr(t,n,i,!1)&&(t.top<=i&&t.left<=n&&(p=e,m=t),!0)},l,s),v=!1;if(m){var y=n-m.left<m.right-n,b=y==c;g=p+(b?0:1),h=b?"after":"before",f=y?m.left:m.right}else{c||g!=s&&g!=l||g++,h=0==g?"after":g==e.text.length?"before":Or(r,o,g-(c?1:0)).bottom+a<=i==c?"after":"before";var w=Kr(r,ge(t,g,h),"line",e,o);f=w.left,v=i<w.top||i>=w.bottom}return $r(t,g=oe(e.text,g,1),h,v,n-f)}function Jr(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==Dr){Dr=A("pre");for(var t=0;t<49;++t)Dr.appendChild(document.createTextNode("x")),Dr.appendChild(A("br"));Dr.appendChild(document.createTextNode("x"))}M(e.measure,Dr);var r=Dr.offsetHeight/50;return 3<r&&(e.cachedTextHeight=r),L(e.measure),r||1}function en(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=A("span","xxxxxxxxxx"),r=A("pre",[t]);M(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return 2<i&&(e.cachedCharWidth=i),i||10}function tn(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a)r[e.options.gutters[a]]=o.offsetLeft+o.clientLeft+i,n[e.options.gutters[a]]=o.clientWidth;return{fixedPos:rn(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function rn(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function nn(n){var i=Jr(n.display),o=n.options.lineWrapping,a=o&&Math.max(5,n.display.scroller.clientWidth/en(n.display)-3);return function(e){if(Ke(n.doc,e))return 0;var t=0;if(e.widgets)for(var r=0;r<e.widgets.length;r++)e.widgets[r].height&&(t+=e.widgets[r].height);return o?t+(Math.ceil(e.text.length/a)||1)*i:t+i}}function on(e){var t=e.doc,r=nn(e);t.iter(function(e){var t=r(e);t!=e.height&&de(e,t)})}function an(e,t,r,n){var i=e.display;if(!r&&"true"==ht(t).getAttribute("cm-not-content"))return null;var o,a,l=i.lineSpace.getBoundingClientRect();try{o=t.clientX-l.left,a=t.clientY-l.top}catch(t){return null}var s,c=Gr(e,o,a);if(n&&1==c.xRel&&(s=se(e.doc,c.line).text).length==c.ch){var u=q(s,s.length,e.options.tabSize)-s.length;c=ge(c.line,Math.max(0,Math.round((o-_r(e.display).left)/en(e.display))-u))}return c}function ln(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function sn(e){e.display.input.showSelection(e.display.input.prepareSelection())}function cn(e,t){void 0===t&&(t=!0);for(var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),a=0;a<r.sel.ranges.length;a++)if(t||a!=r.sel.primIndex){var l=r.sel.ranges[a];if(!(l.from().line>=e.display.viewTo||l.to().line<e.display.viewFrom)){var s=l.empty();(s||e.options.showCursorWhenSelecting)&&un(e,l.head,i),s||fn(e,l,o)}}return n}function un(e,t,r){var n=Kr(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(A("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",n.other){var o=r.appendChild(A("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=n.other.left+"px",o.style.top=n.other.top+"px",o.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function dn(e,t){return e.top-t.top||e.left-t.left}function fn(a,e,t){var r=a.display,n=a.doc,i=document.createDocumentFragment(),o=_r(a.display),T=o.left,L=Math.max(r.sizerWidth,Sr(a)-r.sizer.offsetLeft)-o.right,M="ltr"==n.direction;function N(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),i.appendChild(A("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?L-e:r)+"px;\n                             height: "+(n-t)+"px"))}function l(r,y,b){var w,x,o=se(n,r),k=o.text.length;function _(e,t){return Ur(a,ge(r,e),"div",o,t)}function C(e,t,r){var n=Yr(a,o,null,e),i="ltr"==t==("after"==r)?"left":"right";return _("after"==r?n.begin:n.end-(/\s/.test(o.text.charAt(n.end-1))?2:1),i)[i]}var S=Je(o,n.direction);return function(e,t,r,n){if(!e)return n(t,r,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<r&&a.to>t||t==r&&a.to==t)&&(n(Math.max(a.from,t),Math.min(a.to,r),1==a.level?"rtl":"ltr",o),i=!0)}i||n(t,r,"ltr")}(S,y||0,null==b?k:b,function(e,t,r,n){var i="ltr"==r,o=_(e,i?"left":"right"),a=_(t-1,i?"right":"left"),l=null==y&&0==e,s=null==b&&t==k,c=0==n,u=!S||n==S.length-1;if(a.top-o.top<=3){var d=(M?s:l)&&u,f=(M?l:s)&&c?T:(i?o:a).left,h=d?L:(i?a:o).right;N(f,o.top,h-f,o.bottom)}else{var p,m,g,v;i?(p=M&&l&&c?T:o.left,m=M?L:C(e,r,"before"),g=M?T:C(t,r,"after"),v=M&&s&&u?L:a.right):(p=M?C(e,r,"before"):T,m=!M&&l&&c?L:o.right,g=!M&&s&&u?T:a.left,v=M?C(t,r,"after"):L),N(p,o.top,m-p,o.bottom),o.bottom<a.top&&N(T,o.bottom,null,a.top),N(g,a.top,v-g,a.bottom)}(!w||dn(o,w)<0)&&(w=o),dn(a,w)<0&&(w=a),(!x||dn(o,x)<0)&&(x=o),dn(a,x)<0&&(x=a)}),{start:w,end:x}}var s=e.from(),c=e.to();if(s.line==c.line)l(s.line,s.ch,c.ch);else{var u=se(n,s.line),d=se(n,c.line),f=Be(u)==Be(d),h=l(s.line,s.ch,f?u.text.length+1:null).end,p=l(c.line,f?0:null,c.ch).start;f&&(h.top<p.top-2?(N(h.right,h.top,null,h.bottom),N(T,p.top,p.left,p.bottom)):N(h.right,h.top,p.left-h.right,h.bottom)),h.bottom<p.top&&N(T,h.bottom,null,p.top)}t.appendChild(i)}function hn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",0<e.options.cursorBlinkRate?t.blinker=setInterval(function(){return t.cursorDiv.style.visibility=(r=!r)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function pn(e){e.state.focused||(e.display.input.focus(),gn(e))}function mn(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,vn(e))},100)}function gn(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(it(e,"focus",e,t),e.state.focused=!0,z(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),b&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),hn(e))}function vn(e,t){e.state.delayingBlurEvent||(e.state.focused&&(it(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function yn(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=0;n<t.view.length;n++){var i=t.view[n],o=e.options.lineWrapping,a=void 0,l=0;if(!i.hidden){if(x&&k<8){var s=i.node.offsetTop+i.node.offsetHeight;a=s-r,r=s}else{var c=i.node.getBoundingClientRect();a=c.bottom-c.top,!o&&i.text.firstChild&&(l=i.text.firstChild.getBoundingClientRect().right-c.left-1)}var u=i.line.height-a;if(a<2&&(a=Jr(t)),(.005<u||u<-.005)&&(de(i.line,a),bn(i.line),i.rest))for(var d=0;d<i.rest.length;d++)bn(i.rest[d]);if(l>e.display.sizerWidth){var f=Math.ceil(l/en(e.display));f>e.display.maxLineLength&&(e.display.maxLineLength=f,e.display.maxLine=i.line,e.display.maxLineChanged=!0)}}}}function bn(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var r=e.widgets[t],n=r.node.parentNode;n&&(r.height=n.offsetHeight)}}function wn(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-xr(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=he(t,n),a=he(t,i);if(r&&r.ensure){var l=r.ensure.from.line,s=r.ensure.to.line;l<o?a=he(t,$e(se(t,o=l))+e.wrapper.clientHeight):Math.min(s,t.lastLine())>=a&&(o=he(t,$e(se(t,s))-e.wrapper.clientHeight),a=s)}return{from:o,to:Math.max(a,o+1)}}function xn(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=rn(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",a=0;a<r.length;a++)if(!r[a].hidden){e.options.fixedGutter&&(r[a].gutter&&(r[a].gutter.style.left=o),r[a].gutterBackground&&(r[a].gutterBackground.style.left=o));var l=r[a].alignable;if(l)for(var s=0;s<l.length;s++)l[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function kn(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=me(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(A("div",[A("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,a=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-a)+1,n.lineNumWidth=n.lineNumInnerWidth+a,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",li(e),!0}return!1}function _n(e,t){var r=e.display,n=Jr(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=Tr(e),a={};t.bottom-t.top>o&&(t.bottom=t.top+o);var l=e.doc.height+kr(r),s=t.top<n,c=t.bottom>l-n;if(t.top<i)a.scrollTop=s?0:t.top;else if(t.bottom>i+o){var u=Math.min(t.top,(c?l:t.bottom)-o);u!=i&&(a.scrollTop=u)}var d=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft,f=Sr(e)-(e.options.fixedGutter?r.gutters.offsetWidth:0),h=t.right-t.left>f;return h&&(t.right=t.left+f),t.left<10?a.scrollLeft=0:t.left<d?a.scrollLeft=Math.max(0,t.left-(h?0:10)):t.right>f+d-3&&(a.scrollLeft=t.right+(h?0:10)-f),a}function Cn(e,t){null!=t&&(Ln(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Sn(e){Ln(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Tn(e,t,r){null==t&&null==r||Ln(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function Ln(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,Mn(e,Vr(e,t.from),Vr(e,t.to),t.margin))}function Mn(e,t,r,n){var i=_n(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});Tn(e,i.scrollLeft,i.scrollTop)}function Nn(e,t){Math.abs(e.doc.scrollTop-t)<2||(m||ai(e,{top:t}),An(e,t,!0),m&&ai(e),ti(e,100))}function An(e,t,r){t=Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function On(e,t,r,n){t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,xn(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Dn(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+kr(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+Cr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var zn=function(e,t,r){this.cm=r;var n=this.vert=A("div",[A("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=A("div",[A("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");n.tabIndex=i.tabIndex=-1,e(n),e(i),tt(n,"scroll",function(){n.clientHeight&&t(n.scrollTop,"vertical")}),tt(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,x&&k<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};zn.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&0<e.clientHeight&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},zn.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},zn.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},zn.prototype.zeroWidthHack=function(){var e=w&&!l?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new W,this.disableVert=new W},zn.prototype.enableZeroWidthBar=function(r,n,i){r.style.pointerEvents="auto",n.set(1e3,function e(){var t=r.getBoundingClientRect();("vert"==i?document.elementFromPoint(t.right-1,(t.top+t.bottom)/2):document.elementFromPoint((t.right+t.left)/2,t.bottom-1))!=r?r.style.pointerEvents="none":n.set(1e3,e)})},zn.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var Fn=function(){};function In(e,t){t||(t=Dn(e));var r=e.display.barWidth,n=e.display.barHeight;Pn(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&yn(e),Pn(e,Dn(e)),r=e.display.barWidth,n=e.display.barHeight}function Pn(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}Fn.prototype.update=function(){return{bottom:0,right:0}},Fn.prototype.setScrollLeft=function(){},Fn.prototype.setScrollTop=function(){},Fn.prototype.clear=function(){};var En={native:zn,null:Fn};function qn(r){r.display.scrollbars&&(r.display.scrollbars.clear(),r.display.scrollbars.addClass&&T(r.display.wrapper,r.display.scrollbars.addClass)),r.display.scrollbars=new En[r.options.scrollbarStyle](function(e){r.display.wrapper.insertBefore(e,r.display.scrollbarFiller),tt(e,"mousedown",function(){r.state.focused&&setTimeout(function(){return r.display.input.focus()},0)}),e.setAttribute("cm-not-content","true")},function(e,t){"horizontal"==t?On(r,e):Nn(r,e)},r),r.display.scrollbars.addClass&&z(r.display.wrapper,r.display.scrollbars.addClass)}var Wn=0;function Hn(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:null,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Wn},t=e.curOp,or?or.ops.push(t):t.ownsGroup=or={ops:[t],delayedCallbacks:[]}}function Rn(e){var t=e.curOp;t&&function(e,t){var r=e.ownsGroup;if(r)try{!function(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}(r)}finally{or=null,t(r)}}(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,r=0;r<t.length;r++)Bn(t[r]);for(var n=0;n<t.length;n++)(i=t[n]).updatedDisplay=i.mustUpdate&&ii(i.cm,i.update);var i;for(var o=0;o<t.length;o++)jn(t[o]);for(var a=0;a<t.length;a++)Un(t[a]);for(var l=0;l<t.length;l++)Kn(t[l])}(e)})}function Bn(e){var t,r,n=e.cm,i=n.display;!(r=(t=n).display).scrollbarsClipped&&r.scroller.offsetWidth&&(r.nativeBarWidth=r.scroller.offsetWidth-r.scroller.clientWidth,r.heightForcer.style.height=Cr(t)+"px",r.sizer.style.marginBottom=-r.nativeBarWidth+"px",r.sizer.style.borderRightWidth=Cr(t)+"px",r.scrollbarsClipped=!0),e.updateMaxLine&&Xe(n),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<i.viewFrom||e.scrollToPos.to.line>=i.viewTo)||i.maxLineChanged&&n.options.lineWrapping,e.update=e.mustUpdate&&new ni(n,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function jn(e){var t=e.cm,r=t.display;e.updatedDisplay&&yn(t),e.barMeasure=Dn(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Mr(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+Cr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Sr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function Un(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&On(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==D();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&In(t,e.barMeasure),e.updatedDisplay&&si(t,e.barMeasure),e.selectionChanged&&hn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&pn(e.cm)}function Kn(e){var t=e.cm,r=t.display,n=t.doc;(e.updatedDisplay&&oi(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&An(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&On(t,e.scrollLeft,!0,!0),e.scrollToPos)&&function(e,t){if(!ot(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null;if(t.top+n.top<0?i=!0:t.bottom+n.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!c){var o=A("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-xr(e.display))+"px;\n                         height: "+(t.bottom-t.top+Cr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}(t,function(e,t,r,n){var i;null==n&&(n=0),e.options.lineWrapping||t!=r||(r="before"==(t=t.ch?ge(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t).sticky?ge(t.line,t.ch+1,"before"):t);for(var o=0;o<5;o++){var a=!1,l=Kr(e,t),s=r&&r!=t?Kr(e,r):l,c=_n(e,i={left:Math.min(l.left,s.left),top:Math.min(l.top,s.top)-n,right:Math.max(l.left,s.left),bottom:Math.max(l.bottom,s.bottom)+n}),u=e.doc.scrollTop,d=e.doc.scrollLeft;if(null!=c.scrollTop&&(Nn(e,c.scrollTop),1<Math.abs(e.doc.scrollTop-u)&&(a=!0)),null!=c.scrollLeft&&(On(e,c.scrollLeft),1<Math.abs(e.doc.scrollLeft-d)&&(a=!0)),!a)break}return i}(t,_e(n,e.scrollToPos.from),_e(n,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var a=0;a<i.length;++a)i[a].lines.length||it(i[a],"hide");if(o)for(var l=0;l<o.length;++l)o[l].lines.length&&it(o[l],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&it(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Vn(e,t){if(e.curOp)return t();Hn(e);try{return t()}finally{Rn(e)}}function $n(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Hn(e);try{return t.apply(e,arguments)}finally{Rn(e)}}}function Gn(e){return function(){if(this.curOp)return e.apply(this,arguments);Hn(this);try{return e.apply(this,arguments)}finally{Rn(this)}}}function Xn(t){return function(){var e=this.cm;if(!e||e.curOp)return t.apply(this,arguments);Hn(e);try{return t.apply(this,arguments)}finally{Rn(e)}}}function Yn(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Te&&je(e.doc,t)<i.viewTo&&Qn(e);else if(r<=i.viewFrom)Te&&Ue(e.doc,r+n)>i.viewFrom?Qn(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)Qn(e);else if(t<=i.viewFrom){var o=Jn(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):Qn(e)}else if(r>=i.viewTo){var a=Jn(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):Qn(e)}else{var l=Jn(e,t,t,-1),s=Jn(e,r,r+n,1);l&&s?(i.view=i.view.slice(0,l.index).concat(ir(e,l.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=n):Qn(e)}var c=i.externalMeasured;c&&(r<c.lineN?c.lineN+=n:t<c.lineN+c.size&&(i.externalMeasured=null))}function Zn(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[ln(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);-1==H(a,r)&&a.push(r)}}}function Qn(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function Jn(e,t,r,n){var i,o=ln(e,t),a=e.display.view;if(!Te||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var l=e.display.viewFrom,s=0;s<o;s++)l+=a[s].size;if(l!=t){if(0<n){if(o==a.length-1)return null;i=l+a[o].size-t,o++}else i=l-t;t+=i,r+=i}for(;je(e.doc,r)!=r;){if(o==(n<0?0:a.length-1))return null;r+=n*a[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function ei(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function ti(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,P(ri,e))}function ri(s){var c=s.doc;if(!(c.highlightFrontier>=s.display.viewTo)){var u=+new Date+s.options.workTime,d=Wt(s,c.highlightFrontier),f=[];c.iter(d.line,Math.min(c.first+c.size,s.display.viewTo+500),function(e){if(d.line>=s.display.viewFrom){var t=e.styles,r=e.text.length>s.options.maxHighlightLength?Ot(c.mode,d.state):null,n=Et(s,e,d,!0);r&&(d.state=r),e.styles=n.styles;var i=e.styleClasses,o=n.classes;o?e.styleClasses=o:i&&(e.styleClasses=null);for(var a=!t||t.length!=e.styles.length||i!=o&&(!i||!o||i.bgClass!=o.bgClass||i.textClass!=o.textClass),l=0;!a&&l<t.length;++l)a=t[l]!=e.styles[l];a&&f.push(d.line),e.stateAfter=d.save(),d.nextLine()}else e.text.length<=s.options.maxHighlightLength&&Ht(s,e.text,d),e.stateAfter=d.line%5==0?d.save():null,d.nextLine();if(+new Date>u)return ti(s,s.options.workDelay),!0}),c.highlightFrontier=d.line,c.modeFrontier=Math.max(c.modeFrontier,d.line),f.length&&Vn(s,function(){for(var e=0;e<f.length;e++)Zn(s,f[e],"text")})}}var ni=function(e,t,r){var n=e.display;this.viewport=t,this.visible=wn(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Sr(e),this.force=r,this.dims=tn(e),this.events=[]};function ii(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return Qn(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==ei(e))return!1;kn(e)&&(Qn(e),t.dims=tn(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),a=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>a&&r.viewTo-a<20&&(a=Math.min(i,r.viewTo)),Te&&(o=je(e.doc,o),a=Ue(e.doc,a));var l,s,c,u,d=o!=r.viewFrom||a!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;s=o,c=a,0==(u=(l=e).display).view.length||s>=u.viewTo||c<=u.viewFrom?(u.view=ir(l,s,c),u.viewFrom=s):(u.viewFrom>s?u.view=ir(l,s,u.viewFrom).concat(u.view):u.viewFrom<s&&(u.view=u.view.slice(ln(l,s))),u.viewFrom=s,u.viewTo<c?u.view=u.view.concat(ir(l,u.viewTo,c)):u.viewTo>c&&(u.view=u.view.slice(0,ln(l,c)))),u.viewTo=c,r.viewOffset=$e(se(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var f=ei(e);if(!d&&0==f&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var h=function(e){if(e.hasFocus())return null;var t=D();if(!t||!O(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=window.getSelection();n.anchorNode&&n.extend&&O(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}(e);return 4<f&&(r.lineDiv.style.display="none"),function(r,e,t){var n=r.display,i=r.options.lineNumbers,o=n.lineDiv,a=o.firstChild;function l(e){var t=e.nextSibling;return b&&w&&r.display.currentWheelTarget==e?e.style.display="none":e.parentNode.removeChild(e),t}for(var s=n.view,c=n.viewFrom,u=0;u<s.length;u++){var d=s[u];if(d.hidden);else if(d.node&&d.node.parentNode==o){for(;a!=d.node;)a=l(a);var f=i&&null!=e&&e<=c&&d.lineNumber;d.changes&&(-1<H(d.changes,"gutter")&&(f=!1),cr(r,d,c,t)),f&&(L(d.lineNumber),d.lineNumber.appendChild(document.createTextNode(me(r.options,c)))),a=d.node.nextSibling}else{var h=(g=c,v=t,void 0,y=dr(p=r,m=d),m.text=m.node=y.pre,y.bgClass&&(m.bgClass=y.bgClass),y.textClass&&(m.textClass=y.textClass),hr(p,m),pr(p,m,g,v),gr(p,m,v),m.node);o.insertBefore(h,a)}c+=d.size}var p,m,g,v,y;for(;a;)a=l(a)}(e,r.updateLineNumbers,t.dims),4<f&&(r.lineDiv.style.display=""),r.renderedView=r.view,function(e){if(e&&e.activeElt&&e.activeElt!=D()&&(e.activeElt.focus(),e.anchorNode&&O(document.body,e.anchorNode)&&O(document.body,e.focusNode))){var t=window.getSelection(),r=document.createRange();r.setEnd(e.anchorNode,e.anchorOffset),r.collapse(!1),t.removeAllRanges(),t.addRange(r),t.extend(e.focusNode,e.focusOffset)}}(h),L(r.cursorDiv),L(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,d&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,ti(e,400)),!(r.updateLineNumbers=null)}function oi(e,t){for(var r=t.viewport,n=!0;(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Sr(e)||(r&&null!=r.top&&(r={top:Math.min(e.doc.height+kr(e.display)-Tr(e),r.top)}),t.visible=wn(e.display,e.doc,r),!(t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)))&&ii(e,t);n=!1){yn(e);var i=Dn(e);sn(e),In(e,i),si(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function ai(e,t){var r=new ni(e,t);if(ii(e,r)){yn(e),oi(e,r);var n=Dn(e);sn(e),In(e,n),si(e,n),r.finish()}}function li(e){var t=e.display.gutters.offsetWidth;e.display.sizer.style.marginLeft=t+"px"}function si(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Cr(e)+"px"}function ci(e){var t=e.display.gutters,r=e.options.gutters;L(t);for(var n=0;n<r.length;++n){var i=r[n],o=t.appendChild(A("div",null,"CodeMirror-gutter "+i));"CodeMirror-linenumbers"==i&&((e.display.lineGutter=o).style.width=(e.display.lineNumWidth||1)+"px")}t.style.display=n?"":"none",li(e)}function ui(e){var t=H(e.gutters,"CodeMirror-linenumbers");-1==t&&e.lineNumbers?e.gutters=e.gutters.concat(["CodeMirror-linenumbers"]):-1<t&&!e.lineNumbers&&(e.gutters=e.gutters.slice(0),e.gutters.splice(t,1))}ni.prototype.signal=function(e,t){lt(e,t)&&this.events.push(arguments)},ni.prototype.finish=function(){for(var e=0;e<this.events.length;e++)it.apply(null,this.events[e])};var di=0,fi=null;function hi(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function pi(e){var t=hi(e);return t.x*=fi,t.y*=fi,t}function mi(e,t){var r=hi(t),n=r.x,i=r.y,o=e.display,a=o.scroller,l=a.scrollWidth>a.clientWidth,s=a.scrollHeight>a.clientHeight;if(n&&l||i&&s){if(i&&w&&b)e:for(var c=t.target,u=o.view;c!=a;c=c.parentNode)for(var d=0;d<u.length;d++)if(u[d].node==c){e.display.currentWheelTarget=c;break e}if(n&&!m&&!g&&null!=fi)return i&&s&&Nn(e,Math.max(0,a.scrollTop+i*fi)),On(e,Math.max(0,a.scrollLeft+n*fi)),(!i||i&&s)&&ct(t),void(o.wheelStartX=null);if(i&&null!=fi){var f=i*fi,h=e.doc.scrollTop,p=h+o.wrapper.clientHeight;f<0?h=Math.max(0,h+f-50):p=Math.min(e.doc.height,p+f+50),ai(e,{top:h,bottom:p})}di<20&&(null==o.wheelStartX?(o.wheelStartX=a.scrollLeft,o.wheelStartY=a.scrollTop,o.wheelDX=n,o.wheelDY=i,setTimeout(function(){if(null!=o.wheelStartX){var e=a.scrollLeft-o.wheelStartX,t=a.scrollTop-o.wheelStartY,r=t&&o.wheelDY&&t/o.wheelDY||e&&o.wheelDX&&e/o.wheelDX;o.wheelStartX=o.wheelStartY=null,r&&(fi=(fi*di+r)/(di+1),++di)}},200)):(o.wheelDX+=n,o.wheelDY+=i))}}x?fi=-.53:m?fi=15:a?fi=-.7:s&&(fi=-1/3);var gi=function(e,t){this.ranges=e,this.primIndex=t};gi.prototype.primary=function(){return this.ranges[this.primIndex]},gi.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(!ye(r.anchor,n.anchor)||!ye(r.head,n.head))return!1}return!0},gi.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new vi(be(this.ranges[t].anchor),be(this.ranges[t].head));return new gi(e,this.primIndex)},gi.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},gi.prototype.contains=function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(0<=ve(t,n.from())&&ve(e,n.to())<=0)return r}return-1};var vi=function(e,t){this.anchor=e,this.head=t};function yi(e,t,r){var n=e&&e.options.selectionsMayTouch,i=t[r];t.sort(function(e,t){return ve(e.from(),t.from())}),r=H(t,i);for(var o=1;o<t.length;o++){var a=t[o],l=t[o-1],s=ve(l.to(),a.from());if(n&&!a.empty()?0<s:0<=s){var c=xe(l.from(),a.from()),u=we(l.to(),a.to()),d=l.empty()?a.from()==a.head:l.from()==l.head;o<=r&&--r,t.splice(--o,2,new vi(d?u:c,d?c:u))}}return new gi(t,r)}function bi(e,t){return new gi([new vi(e,t||e)],0)}function wi(e){return e.text?ge(e.from.line+e.text.length-1,X(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function xi(e,t){if(ve(e,t.from)<0)return e;if(ve(e,t.to)<=0)return wi(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=wi(t).ch-t.to.ch),ge(r,n)}function ki(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new vi(xi(i.anchor,t),xi(i.head,t)))}return yi(e.cm,r,e.sel.primIndex)}function _i(e,t,r){return e.line==t.line?ge(r.line,e.ch-t.ch+r.ch):ge(r.line+(e.line-t.line),e.ch)}function Ci(e){e.doc.mode=Mt(e.options,e.doc.modeOption),Si(e)}function Si(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,ti(e,100),e.state.modeGen++,e.curOp&&Yn(e)}function Ti(e,t){return 0==t.from.ch&&0==t.to.ch&&""==X(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Li(e,n,t,i){function o(e){return t?t[e]:null}function r(e,t,r){!function(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),De(e),ze(e,r);var i=n?n(e):1;i!=e.height&&de(e,i)}(e,t,r,i),lr(e,"change",e,n)}function a(e,t){for(var r=[],n=e;n<t;++n)r.push(new $t(c[n],o(n),i));return r}var l=n.from,s=n.to,c=n.text,u=se(e,l.line),d=se(e,s.line),f=X(c),h=o(c.length-1),p=s.line-l.line;if(n.full)e.insert(0,a(0,c.length)),e.remove(c.length,e.size-c.length);else if(Ti(e,n)){var m=a(0,c.length-1);r(d,d.text,h),p&&e.remove(l.line,p),m.length&&e.insert(l.line,m)}else if(u==d)if(1==c.length)r(u,u.text.slice(0,l.ch)+f+u.text.slice(s.ch),h);else{var g=a(1,c.length-1);g.push(new $t(f+u.text.slice(s.ch),h,i)),r(u,u.text.slice(0,l.ch)+c[0],o(0)),e.insert(l.line+1,g)}else if(1==c.length)r(u,u.text.slice(0,l.ch)+c[0]+d.text.slice(s.ch),o(0)),e.remove(l.line+1,p);else{r(u,u.text.slice(0,l.ch)+c[0],o(0)),r(d,f+d.text.slice(s.ch),h);var v=a(1,c.length-1);1<p&&e.remove(l.line+1,p-1),e.insert(l.line+1,v)}lr(e,"change",e,n)}function Mi(e,l,s){!function e(t,r,n){if(t.linked)for(var i=0;i<t.linked.length;++i){var o=t.linked[i];if(o.doc!=r){var a=n&&o.sharedHist;s&&!a||(l(o.doc,a),e(o.doc,t,a))}}}(e,null,!0)}function Ni(e,t){if(t.cm)throw new Error("This document is already in use.");on((e.doc=t).cm=e),Ci(e),Ai(e),e.options.lineWrapping||Xe(e),e.options.mode=t.modeOption,Yn(e)}function Ai(e){("rtl"==e.doc.direction?z:T)(e.display.lineDiv,"CodeMirror-rtl")}function Oi(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Di(e,t){var r={from:be(t.from),to:wi(t),text:ce(e,t.from,t.to)};return Ei(e,r,t.from.line,t.to.line+1),Mi(e,function(e){return Ei(e,r,t.from.line,t.to.line+1)},!0),r}function zi(e){for(;e.length;){if(!X(e).ranges)break;e.pop()}}function Fi(e,t,r,n){var i=e.history;i.undone.length=0;var o,a,l,s=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>s-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=(l=i).lastOp==n?(zi(l.done),X(l.done)):l.done.length&&!X(l.done).ranges?X(l.done):1<l.done.length&&!l.done[l.done.length-2].ranges?(l.done.pop(),X(l.done)):void 0))a=X(o.changes),0==ve(t.from,t.to)&&0==ve(t.from,a.to)?a.to=wi(t):o.changes.push(Di(e,t));else{var c=X(i.done);for(c&&c.ranges||Pi(e.sel,i.done),o={changes:[Di(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=s,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,a||it(e,"historyAdded")}function Ii(e,t,r,n){var i,o,a,l,s,c=e.history,u=n&&n.origin;r==c.lastSelOp||u&&c.lastSelOrigin==u&&(c.lastModTime==c.lastSelTime&&c.lastOrigin==u||(i=e,o=u,a=X(c.done),l=t,"*"==(s=o.charAt(0))||"+"==s&&a.ranges.length==l.ranges.length&&a.somethingSelected()==l.somethingSelected()&&new Date-i.history.lastSelTime<=(i.cm?i.cm.options.historyEventDelay:500)))?c.done[c.done.length-1]=t:Pi(t,c.done),c.lastSelTime=+new Date,c.lastSelOrigin=u,c.lastSelOp=r,n&&!1!==n.clearRedo&&zi(c.undone)}function Pi(e,t){var r=X(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Ei(t,r,e,n){var i=r["spans_"+t.id],o=0;t.iter(Math.max(t.first,e),Math.min(t.first+t.size,n),function(e){e.markedSpans&&((i||(i=r["spans_"+t.id]={}))[o]=e.markedSpans),++o})}function qi(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function Wi(e,t){var r=function(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(qi(r[i]));return n}(e,t),n=Ae(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],a=n[i];if(o&&a)e:for(var l=0;l<a.length;++l){for(var s=a[l],c=0;c<o.length;++c)if(o[c].marker==s.marker)continue e;o.push(s)}else a&&(r[i]=a)}return r}function Hi(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?gi.prototype.deepCopy.call(o):o);else{var a=o.changes,l=[];n.push({changes:l});for(var s=0;s<a.length;++s){var c=a[s],u=void 0;if(l.push({from:c.from,to:c.to,text:c.text}),t)for(var d in c)(u=d.match(/^spans_(\d+)$/))&&-1<H(t,Number(u[1]))&&(X(l)[d]=c[d],delete c[d])}}}return n}function Ri(e,t,r,n){if(n){var i=e.anchor;if(r){var o=ve(t,i)<0;o!=ve(r,i)<0?(i=t,t=r):o!=ve(t,r)<0&&(t=r)}return new vi(i,t)}return new vi(r||t,t)}function Bi(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),$i(e,new gi([Ri(e.sel.primary(),t,r,i)],0),n)}function ji(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=Ri(e.sel.ranges[o],t[o],null,i);$i(e,yi(e.cm,n,e.sel.primIndex),r)}function Ui(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,$i(e,yi(e.cm,i,e.sel.primIndex),n)}function Ki(e,t,r,n){$i(e,bi(t,r),n)}function Vi(e,t,r){var n=e.history.done,i=X(n);i&&i.ranges?Gi(e,n[n.length-1]=t,r):$i(e,t,r)}function $i(e,t,r){Gi(e,t,r),Ii(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function Gi(e,t,r){var n,i,o,a;(lt(e,"beforeSelectionChange")||e.cm&&lt(e.cm,"beforeSelectionChange"))&&(n=e,o=r,a={ranges:(i=t).ranges,update:function(e){this.ranges=[];for(var t=0;t<e.length;t++)this.ranges[t]=new vi(_e(n,e[t].anchor),_e(n,e[t].head))},origin:o&&o.origin},it(n,"beforeSelectionChange",n,a),n.cm&&it(n.cm,"beforeSelectionChange",n.cm,a),t=a.ranges!=i.ranges?yi(n.cm,a.ranges,a.ranges.length-1):i),Xi(e,Zi(e,t,r&&r.bias||(ve(t.primary().head,e.sel.primary().head)<0?-1:1),!0)),r&&!1===r.scroll||!e.cm||Sn(e.cm)}function Xi(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=e.cm.curOp.selectionChanged=!0,at(e.cm)),lr(e,"cursorActivity",e))}function Yi(e){Xi(e,Zi(e,e.sel,null,!1))}function Zi(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],l=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=Ji(e,a.anchor,l&&l.anchor,r,n),c=Ji(e,a.head,l&&l.head,r,n);(i||s!=a.anchor||c!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new vi(s,c))}return i?yi(e.cm,i,t.primIndex):t}function Qi(e,t,r,n,i){var o=se(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var l=o.markedSpans[a],s=l.marker;if((null==l.from||(s.inclusiveLeft?l.from<=t.ch:l.from<t.ch))&&(null==l.to||(s.inclusiveRight?l.to>=t.ch:l.to>t.ch))){if(i&&(it(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!s.atomic)continue;if(r){var c=s.find(n<0?1:-1),u=void 0;if((n<0?s.inclusiveRight:s.inclusiveLeft)&&(c=eo(e,c,-n,c&&c.line==t.line?o:null)),c&&c.line==t.line&&(u=ve(c,r))&&(n<0?u<0:0<u))return Qi(e,c,t,n,i)}var d=s.find(n<0?-1:1);return(n<0?s.inclusiveLeft:s.inclusiveRight)&&(d=eo(e,d,n,d.line==t.line?o:null)),d?Qi(e,d,t,n,i):null}}return t}function Ji(e,t,r,n,i){var o=n||1,a=Qi(e,t,r,o,i)||!i&&Qi(e,t,r,o,!0)||Qi(e,t,r,-o,i)||!i&&Qi(e,t,r,-o,!0);return a||(e.cantEdit=!0,ge(e.first,0))}function eo(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?_e(e,ge(t.line-1)):null:0<r&&t.ch==(n||se(e,t.line)).text.length?t.line<e.first+e.size-1?ge(t.line+1,0):null:new ge(t.line,t.ch+r)}function to(e){e.setSelection(ge(e.firstLine(),0),ge(e.lastLine()),j)}function ro(i,e,t){var o={canceled:!1,from:e.from,to:e.to,text:e.text,origin:e.origin,cancel:function(){return o.canceled=!0}};return t&&(o.update=function(e,t,r,n){e&&(o.from=_e(i,e)),t&&(o.to=_e(i,t)),r&&(o.text=r),void 0!==n&&(o.origin=n)}),it(i,"beforeChange",i,o),i.cm&&it(i.cm,"beforeChange",i.cm,o),o.canceled?null:{from:o.from,to:o.to,text:o.text,origin:o.origin}}function no(e,t,r){if(e.cm){if(!e.cm.curOp)return $n(e.cm,no)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(lt(e,"beforeChange")||e.cm&&lt(e.cm,"beforeChange"))||(t=ro(e,t,!0))){var n=Se&&!r&&function(e,t,r){var n=null;if(e.iter(t.line,r.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=H(n,r)||(n||(n=[])).push(r)}}),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var a=n[o],l=a.find(0),s=0;s<i.length;++s){var c=i[s];if(!(ve(c.to,l.from)<0||0<ve(c.from,l.to))){var u=[s,1],d=ve(c.from,l.from),f=ve(c.to,l.to);(d<0||!a.inclusiveLeft&&!d)&&u.push({from:c.from,to:l.from}),(0<f||!a.inclusiveRight&&!f)&&u.push({from:l.to,to:c.to}),i.splice.apply(i,u),s+=u.length-3}}return i}(e,t.from,t.to);if(n)for(var i=n.length-1;0<=i;--i)io(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else io(e,t)}}function io(e,r){if(1!=r.text.length||""!=r.text[0]||0!=ve(r.from,r.to)){var t=ki(e,r);Fi(e,r,t,e.cm?e.cm.curOp.id:NaN),lo(e,r,t,Ae(e,r));var n=[];Mi(e,function(e,t){t||-1!=H(n,e.history)||(fo(e.history,r),n.push(e.history)),lo(e,r,null,Ae(e,r))})}}function oo(i,o,e){var t=i.cm&&i.cm.state.suppressEdits;if(!t||e){for(var a,r=i.history,n=i.sel,l="undo"==o?r.done:r.undone,s="undo"==o?r.undone:r.done,c=0;c<l.length&&(a=l[c],e?!a.ranges||a.equals(i.sel):a.ranges);c++);if(c!=l.length){for(r.lastOrigin=r.lastSelOrigin=null;;){if(!(a=l.pop()).ranges){if(t)return void l.push(a);break}if(Pi(a,s),e&&!a.equals(i.sel))return void $i(i,a,{clearRedo:!1});n=a}var u=[];Pi(n,s),s.push({changes:u,generation:r.generation}),r.generation=a.generation||++r.maxGeneration;for(var d=lt(i,"beforeChange")||i.cm&&lt(i.cm,"beforeChange"),f=function(e){var r=a.changes[e];if(r.origin=o,d&&!ro(i,r,!1))return l.length=0,{};u.push(Di(i,r));var t=e?ki(i,r):X(l);lo(i,r,t,Wi(i,r)),!e&&i.cm&&i.cm.scrollIntoView({from:r.from,to:wi(r)});var n=[];Mi(i,function(e,t){t||-1!=H(n,e.history)||(fo(e.history,r),n.push(e.history)),lo(e,r,null,Wi(e,r))})},h=a.changes.length-1;0<=h;--h){var p=f(h);if(p)return p.v}}}}function ao(e,t){if(0!=t&&(e.first+=t,e.sel=new gi(Y(e.sel.ranges,function(e){return new vi(ge(e.anchor.line+t,e.anchor.ch),ge(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){Yn(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)Zn(e.cm,n,"gutter")}}function lo(e,t,r,n){if(e.cm&&!e.cm.curOp)return $n(e.cm,lo)(e,t,r,n);if(t.to.line<e.first)ao(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);ao(e,i),t={from:ge(e.first,0),to:ge(t.to.line+i,t.to.ch),text:[X(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:ge(o,se(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=ce(e,t.from,t.to),r||(r=ki(e,t)),e.cm?function(e,t,r){var n=e.doc,i=e.display,o=t.from,a=t.to,l=!1,s=o.line;e.options.lineWrapping||(s=fe(Be(se(n,o.line))),n.iter(s,a.line+1,function(e){if(e==i.maxLine)return l=!0}));-1<n.sel.contains(t.from,t.to)&&at(e);Li(n,t,r,nn(e)),e.options.lineWrapping||(n.iter(s,o.line+t.text.length,function(e){var t=Ge(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,l=!1)}),l&&(e.curOp.updateMaxLine=!0));(function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var r=e.first,n=t-1;r<n;n--){var i=se(e,n).stateAfter;if(i&&(!(i instanceof It)||n+i.lookAhead<t)){r=n+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,r)}})(n,o.line),ti(e,400);var c=t.text.length-(a.line-o.line)-1;t.full?Yn(e):o.line!=a.line||1!=t.text.length||Ti(e.doc,t)?Yn(e,o.line,a.line+1,c):Zn(e,o.line,"text");var u=lt(e,"changes"),d=lt(e,"change");if(d||u){var f={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};d&&lr(e,"change",e,f),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}(e.cm,t,n):Li(e,t,n),Gi(e,r,j)}}function so(e,t,r,n,i){var o;n||(n=r),ve(n,r)<0&&(r=(o=[n,r])[0],n=o[1]),"string"==typeof t&&(t=e.splitLines(t)),no(e,{from:r,to:n,text:t,origin:i})}function co(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function uo(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var l=0;l<o.ranges.length;l++)co(o.ranges[l].anchor,t,r,n),co(o.ranges[l].head,t,r,n)}else{for(var s=0;s<o.changes.length;++s){var c=o.changes[s];if(r<c.from.line)c.from=ge(c.from.line+n,c.from.ch),c.to=ge(c.to.line+n,c.to.ch);else if(t<=c.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function fo(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;uo(e.done,r,n,i),uo(e.undone,r,n,i)}function ho(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=se(e,ke(e,t)):i=fe(t),null==i?null:(n(o,i)&&e.cm&&Zn(e.cm,i,r),o)}function po(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function mo(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}vi.prototype.from=function(){return xe(this.anchor,this.head)},vi.prototype.to=function(){return we(this.anchor,this.head)},vi.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},po.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r,n=e,i=e+t;n<i;++n){var o=this.lines[n];this.height-=o.height,(r=o).parent=null,De(r),lr(o,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},mo.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var r=0;r<this.children.length;++r){var n=this.children[r],i=n.chunkSize();if(e<i){var o=Math.min(t,i-e),a=n.height;if(n.removeInner(e,o),this.height-=a-n.height,i==o&&(this.children.splice(r--,1),n.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(1<this.children.length||!(this.children[0]instanceof po))){var l=[];this.collapse(l),this.children=[new po(l)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){this.size+=t.length,this.height+=r;for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,r),i.lines&&50<i.lines.length){for(var a=i.lines.length%25+25,l=a;l<i.lines.length;){var s=new po(i.lines.slice(l,l+=25));i.height-=s.height,this.children.splice(++n,0,s),s.parent=this}i.lines=i.lines.slice(0,a),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new mo(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=H(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new mo(e.children);(n.parent=e).children=[n,t],e=n}t.parent=e.parent}while(10<e.children.length);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e);if(i.iterN(e,a,r))return!0;if(0==(t-=a))break;e=0}else e-=o}}};var go=function(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t};function vo(e,t,r){$e(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Cn(e,r)}go.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=fe(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=br(this);de(r,Math.max(0,r.height-o)),e&&(Vn(e,function(){vo(e,r,-o),Zn(e,n,"widget")}),lr(e,"lineWidgetCleared",e,this,n))}},go.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=br(this)-t;i&&(Ke(this.doc,n)||de(n,n.height+i),r&&Vn(r,function(){r.curOp.forceUpdate=!0,vo(r,n,i),lr(r,"lineWidgetChanged",r,e,fe(n))}))},st(go);var yo=0,bo=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++yo};function wo(t,n,i,e,r){if(e&&e.shared)return function(e,r,n,i,o){(i=E(i)).shared=!1;var a=[wo(e,r,n,i,o)],l=a[0],s=i.widgetNode;return Mi(e,function(e){s&&(i.widgetNode=s.cloneNode(!0)),a.push(wo(e,_e(e,r),_e(e,n),i,o));for(var t=0;t<e.linked.length;++t)if(e.linked[t].isParent)return;l=X(a)}),new xo(a,l)}(t,n,i,e,r);if(t.cm&&!t.cm.curOp)return $n(t.cm,wo)(t,n,i,e,r);var o=new bo(t,r),a=ve(n,i);if(e&&E(e,o,!1),0<a||0==a&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=N("span",[o.replacedWith],"CodeMirror-widget"),e.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),e.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(Re(t,n.line,n,i,o)||n.line!=i.line&&Re(t,i.line,n,i,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Te=!0}o.addToHistory&&Fi(t,{from:n,to:i,origin:"markText"},t.sel,NaN);var l,s=n.line,c=t.cm;if(t.iter(s,i.line+1,function(e){var t,r;c&&o.collapsed&&!c.options.lineWrapping&&Be(e)==c.display.maxLine&&(l=!0),o.collapsed&&s!=n.line&&de(e,0),t=e,r=new Le(o,s==n.line?n.ch:null,s==i.line?i.ch:null),t.markedSpans=t.markedSpans?t.markedSpans.concat([r]):[r],r.marker.attachLine(t),++s}),o.collapsed&&t.iter(n.line,i.line+1,function(e){Ke(t,e)&&de(e,0)}),o.clearOnEnter&&tt(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(Se=!0,(t.history.done.length||t.history.undone.length)&&t.clearHistory()),o.collapsed&&(o.id=++yo,o.atomic=!0),c){if(l&&(c.curOp.updateMaxLine=!0),o.collapsed)Yn(c,n.line,i.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var u=n.line;u<=i.line;u++)Zn(c,u,"text");o.atomic&&Yi(c.doc),lr(c,"markerAdded",c,o)}return o}bo.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){var t=this.doc.cm,r=t&&!t.curOp;if(r&&Hn(t),lt(this,"clear")){var n=this.find();n&&lr(this,"clear",n.from,n.to)}for(var i=null,o=null,a=0;a<this.lines.length;++a){var l=e.lines[a],s=Me(l.markedSpans,e);t&&!e.collapsed?Zn(t,fe(l),"text"):t&&(null!=s.to&&(o=fe(l)),null!=s.from&&(i=fe(l))),l.markedSpans=Ne(l.markedSpans,s),null==s.from&&e.collapsed&&!Ke(e.doc,l)&&t&&de(l,Jr(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var c=0;c<this.lines.length;++c){var u=Be(e.lines[c]),d=Ge(u);d>t.display.maxLineLength&&(t.display.maxLine=u,t.display.maxLineLength=d,t.display.maxLineChanged=!0)}null!=i&&t&&this.collapsed&&Yn(t,i,o+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&Yi(t.doc)),t&&lr(t,"markerCleared",t,this,i,o),r&&Rn(t),this.parent&&this.parent.clear()}},bo.prototype.find=function(e,t){var r,n;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],a=Me(o.markedSpans,this);if(null!=a.from&&(r=ge(t?o:fe(o),a.from),-1==e))return r;if(null!=a.to&&(n=ge(t?o:fe(o),a.to),1==e))return n}return r&&{from:r,to:n}},bo.prototype.changed=function(){var o=this,a=this.find(-1,!0),l=this,s=this.doc.cm;a&&s&&Vn(s,function(){var e=a.line,t=fe(a.line),r=Nr(s,t);if(r&&(Pr(r),s.curOp.selectionChanged=s.curOp.forceUpdate=!0),s.curOp.updateMaxLine=!0,!Ke(l.doc,e)&&null!=l.height){var n=l.height;l.height=null;var i=br(l)-n;i&&de(e,e.height+i)}lr(s,"markerChanged",s,o)})},bo.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=H(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},bo.prototype.detachLine=function(e){if(this.lines.splice(H(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},st(bo);var xo=function(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this};function ko(e){return e.findMarks(ge(e.first,0),e.clipPos(ge(e.lastLine())),function(e){return e.parent})}function _o(o){for(var e=function(e){var t=o[e],r=[t.primary.doc];Mi(t.primary.doc,function(e){return r.push(e)});for(var n=0;n<t.markers.length;n++){var i=t.markers[n];-1==H(r,i.doc)&&(i.parent=null,t.markers.splice(n--,1))}},t=0;t<o.length;t++)e(t)}xo.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();lr(this,"clear")}},xo.prototype.find=function(e,t){return this.primary.find(e,t)},st(xo);var Co=0,So=function(e,t,r,n,i){if(!(this instanceof So))return new So(e,t,r,n,i);null==r&&(r=0),mo.call(this,[new po([new $t("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=ge(r,0);this.sel=bi(o),this.history=new Oi(null),this.id=++Co,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),Li(this,{from:o,to:o,text:e}),$i(this,bi(o),j)};So.prototype=Q(mo.prototype,{constructor:So,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=ue(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:Xn(function(e){var t=ge(this.first,0),r=this.first+this.size-1;no(this,{from:t,to:ge(r,se(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Tn(this.cm,0,0),$i(this,bi(t),j)}),replaceRange:function(e,t,r,n){so(this,e,t=_e(this,t),r=r?_e(this,r):t,n)},getRange:function(e,t,r){var n=ce(this,_e(this,e),_e(this,t));return!1===r?n:n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(pe(this,e))return se(this,e)},getLineNumber:function(e){return fe(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=se(this,e)),Be(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return _e(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Xn(function(e,t,r){Ki(this,_e(this,"number"==typeof e?ge(e,t||0):e),null,r)}),setSelection:Xn(function(e,t,r){Ki(this,_e(this,e),_e(this,t||e),r)}),extendSelection:Xn(function(e,t,r){Bi(this,_e(this,e),t&&_e(this,t),r)}),extendSelections:Xn(function(e,t){ji(this,Ce(this,e),t)}),extendSelectionsBy:Xn(function(e,t){ji(this,Ce(this,Y(this.sel.ranges,e)),t)}),setSelections:Xn(function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new vi(_e(this,e[i].anchor),_e(this,e[i].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),$i(this,yi(this.cm,n,t),r)}}),addSelection:Xn(function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new vi(_e(this,e),_e(this,t||e))),$i(this,yi(this.cm,n,n.length-1),r)}),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=ce(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=ce(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:Xn(function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var a=i.ranges[o];n[o]={from:a.from(),to:a.to(),text:this.splitLines(e[o]),origin:r}}for(var l=t&&"end"!=t&&function(e,t,r){for(var n=[],i=ge(e.first,0),o=i,a=0;a<t.length;a++){var l=t[a],s=_i(l.from,i,o),c=_i(wi(l),i,o);if(i=l.to,o=c,"around"==r){var u=e.sel.ranges[a],d=ve(u.head,u.anchor)<0;n[a]=new vi(d?c:s,d?s:c)}else n[a]=new vi(s,s)}return new gi(n,e.sel.primIndex)}(this,n,t),s=n.length-1;0<=s;s--)no(this,n[s]);l?Vi(this,l):this.cm&&Sn(this.cm)}),undo:Xn(function(){oo(this,"undo")}),redo:Xn(function(){oo(this,"redo")}),undoSelection:Xn(function(){oo(this,"undo",!0)}),redoSelection:Xn(function(){oo(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){this.history=new Oi(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Hi(this.history.done),undone:Hi(this.history.undone)}},setHistory:function(e){var t=this.history=new Oi(this.history.maxGeneration);t.done=Hi(e.done.slice(0),null,!0),t.undone=Hi(e.undone.slice(0),null,!0)},setGutterMarker:Xn(function(e,r,n){return ho(this,e,"gutter",function(e){var t=e.gutterMarkers||(e.gutterMarkers={});return!(t[r]=n)&&re(t)&&(e.gutterMarkers=null),!0})}),clearGutter:Xn(function(t){var r=this;this.iter(function(e){e.gutterMarkers&&e.gutterMarkers[t]&&ho(r,e,"gutter",function(){return e.gutterMarkers[t]=null,re(e.gutterMarkers)&&(e.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!pe(this,e))return null;if(!(e=se(this,t=e)))return null}else if(null==(t=fe(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Xn(function(e,r,n){return ho(this,e,"gutter"==r?"gutter":"class",function(e){var t="text"==r?"textClass":"background"==r?"bgClass":"gutter"==r?"gutterClass":"wrapClass";if(e[t]){if(C(n).test(e[t]))return!1;e[t]+=" "+n}else e[t]=n;return!0})}),removeLineClass:Xn(function(e,o,a){return ho(this,e,"gutter"==o?"gutter":"class",function(e){var t="text"==o?"textClass":"background"==o?"bgClass":"gutter"==o?"gutterClass":"wrapClass",r=e[t];if(!r)return!1;if(null==a)e[t]=null;else{var n=r.match(C(a));if(!n)return!1;var i=n.index+n[0].length;e[t]=r.slice(0,n.index)+(n.index&&i!=r.length?" ":"")+r.slice(i)||null}return!0})}),addLineWidget:Xn(function(e,t,r){return i=e,o=new go(n=this,t,r),(a=n.cm)&&o.noHScroll&&(a.display.alignWidgets=!0),ho(n,i,"widget",function(e){var t=e.widgets||(e.widgets=[]);if(null==o.insertAt?t.push(o):t.splice(Math.min(t.length-1,Math.max(0,o.insertAt)),0,o),o.line=e,a&&!Ke(n,e)){var r=$e(e)<n.scrollTop;de(e,e.height+br(o)),r&&Cn(a,o.height),a.curOp.forceUpdate=!0}return!0}),a&&lr(a,"lineWidgetAdded",a,o,"number"==typeof i?i:fe(i)),o;var n,i,o,a}),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return wo(this,_e(this,e),_e(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return wo(this,e=_e(this,e),e,r,"bookmark")},findMarksAt:function(e){var t=[],r=se(this,(e=_e(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(i,o,a){i=_e(this,i),o=_e(this,o);var l=[],s=i.line;return this.iter(i.line,o.line+1,function(e){var t=e.markedSpans;if(t)for(var r=0;r<t.length;r++){var n=t[r];null!=n.to&&s==i.line&&i.ch>=n.to||null==n.from&&s!=i.line||null!=n.from&&s==o.line&&n.from>=o.ch||a&&!a(n.marker)||l.push(n.marker.parent||n.marker)}++s}),l},getAllMarks:function(){var n=[];return this.iter(function(e){var t=e.markedSpans;if(t)for(var r=0;r<t.length;++r)null!=t[r].from&&n.push(t[r].marker)}),n},posFromIndex:function(r){var n,i=this.first,o=this.lineSeparator().length;return this.iter(function(e){var t=e.text.length+o;if(r<t)return n=r,!0;r-=t,++i}),_e(this,ge(i,n))},indexFromPos:function(e){var t=(e=_e(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+r}),t},copy:function(e){var t=new So(ue(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new So(ue(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(ve(o,a)){var l=wo(e,o,a,n.primary,n.primary.type);n.markers.push(l),l.parent=n}}}(n,ko(this)),n},unlinkDoc:function(e){if(e instanceof ka&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),_o(ko(this));break}}if(e.history==this.history){var r=[e.id];Mi(e,function(e){return r.push(e.id)},!0),e.history=new Oi(null),e.history.done=Hi(this.history.done,r),e.history.undone=Hi(this.history.undone,r)}},iterLinkedDocs:function(e){Mi(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):xt(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:Xn(function(e){var t;("rtl"!=e&&(e="ltr"),e!=this.direction)&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&Vn(t=this.cm,function(){Ai(t),Yn(t)}))})}),So.prototype.eachLine=So.prototype.iter;var To=0;function Lo(e){var i=this;if(Mo(i),!ot(i,e)&&!wr(i.display,e)){ct(e),x&&(To=+new Date);var o=an(i,e,!0),t=e.dataTransfer.files;if(o&&!i.isReadOnly())if(t&&t.length&&window.FileReader&&window.File)for(var a=t.length,l=Array(a),s=0,r=function(e,r){if(!i.options.allowDropFileTypes||-1!=H(i.options.allowDropFileTypes,e.type)){var n=new FileReader;n.onload=$n(i,function(){var e=n.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),l[r]=e,++s==a){var t={from:o=_e(i.doc,o),to:o,text:i.doc.splitLines(l.join(i.doc.lineSeparator())),origin:"paste"};no(i.doc,t),Vi(i.doc,bi(o,wi(t)))}}),n.readAsText(e)}},n=0;n<a;++n)r(t[n],n);else{if(i.state.draggingText&&-1<i.doc.sel.contains(o))return i.state.draggingText(e),void setTimeout(function(){return i.display.input.focus()},20);try{var c=e.dataTransfer.getData("Text");if(c){var u;if(i.state.draggingText&&!i.state.draggingText.copy&&(u=i.listSelections()),Gi(i.doc,bi(o,o)),u)for(var d=0;d<u.length;++d)so(i.doc,"",u[d].anchor,u[d].head,"drag");i.replaceSelection(c,"around","paste"),i.display.input.focus()}}catch(e){}}}}function Mo(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function No(t){if(document.getElementsByClassName){for(var e=document.getElementsByClassName("CodeMirror"),r=[],n=0;n<e.length;n++){var i=e[n].CodeMirror;i&&r.push(i)}r.length&&r[0].operation(function(){for(var e=0;e<r.length;e++)t(r[e])})}}var Ao=!1;function Oo(){var e;Ao||(tt(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,No(Do)},100))}),tt(window,"blur",function(){return No(vn)}),Ao=!0)}function Do(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var zo={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",127:"Delete",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Fo=0;Fo<10;Fo++)zo[Fo+48]=zo[Fo+96]=String(Fo);for(var Io=65;Io<=90;Io++)zo[Io]=String.fromCharCode(Io);for(var Po=1;Po<=12;Po++)zo[Po+111]=zo[Po+63235]="F"+Po;var Eo={};function qo(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var a=0;a<o.length-1;a++){var l=o[a];if(/^(cmd|meta|m)$/i.test(l))i=!0;else if(/^a(lt)?$/i.test(l))t=!0;else if(/^(c|ctrl|control)$/i.test(l))r=!0;else{if(!/^s(hift)?$/i.test(l))throw new Error("Unrecognized modifier name: "+l);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Wo(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=Y(r.split(" "),qo),o=0;o<i.length;o++){var a=void 0,l=void 0;o==i.length-1?(l=i.join(" "),a=n):(l=i.slice(0,o+1).join(" "),a="...");var s=t[l];if(s){if(s!=a)throw new Error("Inconsistent bindings for "+l)}else t[l]=a}delete e[r]}for(var c in t)e[c]=t[c];return e}function Ho(e,t,r,n){var i=(t=Uo(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Ho(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var a=Ho(e,t.fallthrough[o],r,n);if(a)return a}}}function Ro(e){var t="string"==typeof e?e:zo[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Bo(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(y?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(y?t.ctrlKey:t.metaKey)&&"Cmd"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function jo(e,t){if(g&&34==e.keyCode&&e.char)return!1;var r=zo[e.keyCode];return null!=r&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(r=e.code),Bo(r,e,t))}function Uo(e){return"string"==typeof e?Eo[e]:e}function Ko(t,e){for(var r=t.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=e(r[i]);n.length&&ve(o.from,X(n).to)<=0;){var a=n.pop();if(ve(a.from,o.from)<0){o.from=a.from;break}}n.push(o)}Vn(t,function(){for(var e=n.length-1;0<=e;e--)so(t.doc,"",n[e].from,n[e].to,"+delete");Sn(t)})}function Vo(e,t,r){var n=oe(e.text,t+r,r);return n<0||n>e.text.length?null:n}function $o(e,t,r){var n=Vo(e,t.ch,r);return null==n?null:new ge(t.line,n,r<0?"after":"before")}function Go(e,t,r,n,i){if(e){var o=Je(r,t.doc.direction);if(o){var a,l=i<0?X(o):o[0],s=i<0==(1==l.level)?"after":"before";if(0<l.level||"rtl"==t.doc.direction){var c=Ar(t,r);a=i<0?r.text.length-1:0;var u=Or(t,c,a).top;a=ae(function(e){return Or(t,c,e).top==u},i<0==(1==l.level)?l.from:l.to-1,a),"before"==s&&(a=Vo(r,a,1))}else a=i<0?l.to:l.from;return new ge(n,a,s)}}return new ge(n,i<0?r.text.length:0,i<0?"before":"after")}Eo.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Eo.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Eo.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Eo.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Eo.default=w?Eo.macDefault:Eo.pcDefault;var Xo={selectAll:to,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),j)},killLine:function(r){return Ko(r,function(e){if(e.empty()){var t=se(r.doc,e.head.line).text.length;return e.head.ch==t&&e.head.line<r.lastLine()?{from:e.head,to:ge(e.head.line+1,0)}:{from:e.head,to:ge(e.head.line,t)}}return{from:e.from(),to:e.to()}})},deleteLine:function(t){return Ko(t,function(e){return{from:ge(e.from().line,0),to:_e(t.doc,ge(e.to().line+1,0))}})},delLineLeft:function(e){return Ko(e,function(e){return{from:ge(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(r){return Ko(r,function(e){var t=r.charCoords(e.head,"div").top+5;return{from:r.coordsChar({left:0,top:t},"div"),to:e.from()}})},delWrappedLineRight:function(n){return Ko(n,function(e){var t=n.charCoords(e.head,"div").top+5,r=n.coordsChar({left:n.display.lineDiv.offsetWidth+100,top:t},"div");return{from:e.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(ge(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(ge(e.lastLine()))},goLineStart:function(t){return t.extendSelectionsBy(function(e){return Yo(t,e.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(t){return t.extendSelectionsBy(function(e){return Zo(t,e.head)},{origin:"+move",bias:1})},goLineEnd:function(t){return t.extendSelectionsBy(function(e){return function(e,t){var r=se(e.doc,t),n=function(e){for(var t;t=We(e);)e=t.find(1,!0).line;return e}(r);n!=r&&(t=fe(n));return Go(!0,e,r,t,-1)}(t,e.head.line)},{origin:"+move",bias:-1})},goLineRight:function(r){return r.extendSelectionsBy(function(e){var t=r.cursorCoords(e.head,"div").top+5;return r.coordsChar({left:r.display.lineDiv.offsetWidth+100,top:t},"div")},K)},goLineLeft:function(r){return r.extendSelectionsBy(function(e){var t=r.cursorCoords(e.head,"div").top+5;return r.coordsChar({left:0,top:t},"div")},K)},goLineLeftSmart:function(n){return n.extendSelectionsBy(function(e){var t=n.cursorCoords(e.head,"div").top+5,r=n.coordsChar({left:0,top:t},"div");return r.ch<n.getLine(r.line).search(/\S/)?Zo(n,e.head):r},K)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),a=q(e.getLine(o.line),o.ch,n);t.push(G(n-a%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(a){return Vn(a,function(){for(var e=a.listSelections(),t=[],r=0;r<e.length;r++)if(e[r].empty()){var n=e[r].head,i=se(a.doc,n.line).text;if(i)if(n.ch==i.length&&(n=new ge(n.line,n.ch-1)),0<n.ch)n=new ge(n.line,n.ch+1),a.replaceRange(i.charAt(n.ch-1)+i.charAt(n.ch-2),ge(n.line,n.ch-2),n,"+transpose");else if(n.line>a.doc.first){var o=se(a.doc,n.line-1).text;o&&(n=new ge(n.line,1),a.replaceRange(i.charAt(0)+a.doc.lineSeparator()+o.charAt(o.length-1),ge(n.line-1,o.length-1),n,"+transpose"))}t.push(new vi(n,n))}a.setSelections(t)})},newlineAndIndent:function(n){return Vn(n,function(){for(var e=n.listSelections(),t=e.length-1;0<=t;t--)n.replaceRange(n.doc.lineSeparator(),e[t].anchor,e[t].head,"+input");e=n.listSelections();for(var r=0;r<e.length;r++)n.indentLine(e[r].from().line,null,!0);Sn(n)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Yo(e,t){var r=se(e.doc,t),n=Be(r);return n!=r&&(t=fe(n)),Go(!0,e,n,t,1)}function Zo(e,t){var r=Yo(e,t.line),n=se(e.doc,r.line),i=Je(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(0,n.text.search(/\S/)),a=t.line==r.line&&t.ch<=o&&t.ch;return ge(r.line,a?0:o,r.sticky)}return r}function Qo(e,t,r){if("string"==typeof t&&!(t=Xo[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=B}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}var Jo=new W;function ea(e,t,r,n){var i=e.state.keySeq;if(i){if(Ro(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:Jo.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),ta(e,i+" "+t,r,n))return!0}return ta(e,t,r,n)}function ta(e,t,r,n){var i=function(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=Ho(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&Ho(t,e.options.extraKeys,r,e)||Ho(t,e.options.keyMap,r,e)}(e,t,n);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&lr(e,"keyHandled",e,t,r),"handled"!=i&&"multi"!=i||(ct(r),hn(e)),!!i}function ra(t,e){var r=jo(e,!0);return!!r&&(e.shiftKey&&!t.state.keySeq?ea(t,"Shift-"+r,e,function(e){return Qo(t,e,!0)})||ea(t,r,e,function(e){if("string"==typeof e?/^go[A-Z]/.test(e):e.motion)return Qo(t,e)}):ea(t,r,e,function(e){return Qo(t,e)}))}var na=null;function ia(e){var t=this;if(t.curOp.focus=D(),!ot(t,e)){x&&k<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var n=ra(t,e);g&&(na=n?r:null,!n&&88==r&&!_t&&(w?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),nt(document,"keyup",r),nt(document,"mouseover",r))}z(t,"CodeMirror-crosshair"),tt(document,"keyup",r),tt(document,"mouseover",r)}(t)}}function oa(e){16==e.keyCode&&(this.doc.sel.shift=!1),ot(this,e)}function aa(e){var t=this;if(!(wr(t.display,e)||ot(t,e)||e.ctrlKey&&!e.altKey||w&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(g&&r==na)return na=null,void ct(e);if(!g||e.which&&!(e.which<10)||!ra(t,e)){var i,o=String.fromCharCode(null==n?r:n);if("\b"!=o)if(!ea(i=t,"'"+o+"'",e,function(e){return Qo(i,e,!0)}))t.display.input.onKeyPress(e)}}}var la,sa,ca=function(e,t,r){this.time=e,this.pos=t,this.button=r};function ua(e){var t=this,r=t.display;if(!(ot(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,wr(r,e))b||(r.scroller.draggable=!1,setTimeout(function(){return r.scroller.draggable=!0},100));else if(!ha(t,e)){var n,i,o,a=an(t,e),l=pt(e),s=a?(n=a,i=l,o=+new Date,sa&&sa.compare(o,n,i)?(la=sa=null,"triple"):la&&la.compare(o,n,i)?(sa=new ca(o,n,i),la=null,"double"):(la=new ca(o,n,i),sa=null,"single")):"single";window.focus(),1==l&&t.state.selectingText&&t.state.selectingText(e),a&&function(r,e,n,t,i){var o="Click";"double"==t?o="Double"+o:"triple"==t&&(o="Triple"+o);return ea(r,Bo(o=(1==e?"Left":2==e?"Middle":"Right")+o,i),i,function(e){if("string"==typeof e&&(e=Xo[e]),!e)return!1;var t=!1;try{r.isReadOnly()&&(r.state.suppressEdits=!0),t=e(r,n)!=B}finally{r.state.suppressEdits=!1}return t})}(t,l,a,s,e)||(1==l?a?function(e,t,r,n){x?setTimeout(P(pn,e),0):e.curOp.focus=D();var i,o=function(e,t,r){var n=e.getOption("configureMouse"),i=n?n(e,t,r):{};if(null==i.unit){var o=h?r.shiftKey&&r.metaKey:r.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||r.shiftKey);null==i.addNew&&(i.addNew=w?r.metaKey:r.ctrlKey);null==i.moveOnDrag&&(i.moveOnDrag=!(w?r.altKey:r.ctrlKey));return i}(e,r,n),a=e.doc.sel;e.options.dragDrop&&vt&&!e.isReadOnly()&&"single"==r&&-1<(i=a.contains(t))&&(ve((i=a.ranges[i]).from(),t)<0||0<t.xRel)&&(0<ve(i.to(),t)||t.xRel<0)?function(t,r,n,i){var o=t.display,a=!1,l=$n(t,function(e){b&&(o.scroller.draggable=!1),t.state.draggingText=!1,nt(o.wrapper.ownerDocument,"mouseup",l),nt(o.wrapper.ownerDocument,"mousemove",s),nt(o.scroller,"dragstart",c),nt(o.scroller,"drop",l),a||(ct(e),i.addNew||Bi(t.doc,n,null,null,i.extend),b||x&&9==k?setTimeout(function(){o.wrapper.ownerDocument.body.focus(),o.input.focus()},20):o.input.focus())}),s=function(e){a=a||10<=Math.abs(r.clientX-e.clientX)+Math.abs(r.clientY-e.clientY)},c=function(){return a=!0};b&&(o.scroller.draggable=!0);(t.state.draggingText=l).copy=!i.moveOnDrag,o.scroller.dragDrop&&o.scroller.dragDrop();tt(o.wrapper.ownerDocument,"mouseup",l),tt(o.wrapper.ownerDocument,"mousemove",s),tt(o.scroller,"dragstart",c),tt(o.scroller,"drop",l),mn(t),setTimeout(function(){return o.input.focus()},20)}(e,n,t,o):function(g,e,v,y){var a=g.display,b=g.doc;ct(e);var w,x,k=b.sel,t=k.ranges;y.addNew&&!y.extend?(x=b.sel.contains(v),w=-1<x?t[x]:new vi(v,v)):(w=b.sel.primary(),x=b.sel.primIndex);if("rectangle"==y.unit)y.addNew||(w=new vi(v,v)),v=an(g,e,!0,!0),x=-1;else{var r=da(g,v,y.unit);w=y.extend?Ri(w,r.anchor,r.head,y.extend):r}y.addNew?-1==x?(x=t.length,$i(b,yi(g,t.concat([w]),x),{scroll:!1,origin:"*mouse"})):1<t.length&&t[x].empty()&&"char"==y.unit&&!y.extend?($i(b,yi(g,t.slice(0,x).concat(t.slice(x+1)),0),{scroll:!1,origin:"*mouse"}),k=b.sel):Ui(b,x,w,U):($i(b,new gi([w],x=0),U),k=b.sel);var _=v;function l(e){if(0!=ve(_,e))if(_=e,"rectangle"==y.unit){for(var t=[],r=g.options.tabSize,n=q(se(b,v.line).text,v.ch,r),i=q(se(b,e.line).text,e.ch,r),o=Math.min(n,i),a=Math.max(n,i),l=Math.min(v.line,e.line),s=Math.min(g.lastLine(),Math.max(v.line,e.line));l<=s;l++){var c=se(b,l).text,u=V(c,o,r);o==a?t.push(new vi(ge(l,u),ge(l,u))):c.length>u&&t.push(new vi(ge(l,u),ge(l,V(c,a,r))))}t.length||t.push(new vi(v,v)),$i(b,yi(g,k.ranges.slice(0,x).concat(t),x),{origin:"*mouse",scroll:!1}),g.scrollIntoView(e)}else{var d,f=w,h=da(g,e,y.unit),p=f.anchor;0<ve(h.anchor,p)?(d=h.head,p=xe(f.from(),h.anchor)):(d=h.anchor,p=we(f.to(),h.head));var m=k.ranges.slice(0);m[x]=function(e,t){var r=t.anchor,n=t.head,i=se(e.doc,r.line);if(0==ve(r,n)&&r.sticky==n.sticky)return t;var o=Je(i);if(!o)return t;var a=Ze(o,r.ch,r.sticky),l=o[a];if(l.from!=r.ch&&l.to!=r.ch)return t;var s,c=a+(l.from==r.ch==(1!=l.level)?0:1);if(0==c||c==o.length)return t;if(n.line!=r.line)s=0<(n.line-r.line)*("ltr"==e.doc.direction?1:-1);else{var u=Ze(o,n.ch,n.sticky),d=u-a||(n.ch-r.ch)*(1==l.level?-1:1);s=u==c-1||u==c?d<0:0<d}var f=o[c+(s?-1:0)],h=s==(1==f.level),p=h?f.from:f.to,m=h?"after":"before";return r.ch==p&&r.sticky==m?t:new vi(new ge(r.line,p,m),n)}(g,new vi(_e(b,p),d)),$i(b,yi(g,m,x),U)}}var s=a.wrapper.getBoundingClientRect(),c=0;function n(e){g.state.selectingText=!1,c=1/0,ct(e),a.input.focus(),nt(a.wrapper.ownerDocument,"mousemove",i),nt(a.wrapper.ownerDocument,"mouseup",o),b.history.lastSelOrigin=null}var i=$n(g,function(e){0!==e.buttons&&pt(e)?function e(t){var r=++c;var n=an(g,t,!0,"rectangle"==y.unit);if(!n)return;if(0!=ve(n,_)){g.curOp.focus=D(),l(n);var i=wn(a,b);(n.line>=i.to||n.line<i.from)&&setTimeout($n(g,function(){c==r&&e(t)}),150)}else{var o=t.clientY<s.top?-20:t.clientY>s.bottom?20:0;o&&setTimeout($n(g,function(){c==r&&(a.scroller.scrollTop+=o,e(t))}),50)}}(e):n(e)}),o=$n(g,n);g.state.selectingText=o,tt(a.wrapper.ownerDocument,"mousemove",i),tt(a.wrapper.ownerDocument,"mouseup",o)}(e,n,t,o)}(t,a,s,e):ht(e)==r.scroller&&ct(e):2==l?(a&&Bi(t.doc,a),setTimeout(function(){return r.input.focus()},20)):3==l&&(_?t.display.input.onContextMenu(e):mn(t)))}}function da(e,t,r){if("char"==r)return new vi(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new vi(ge(t.line,0),_e(e.doc,ge(t.line+1,0)));var n=r(e,t);return new vi(n.from,n.to)}function fa(e,t,r,n){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(t){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&ct(t);var a=e.display,l=a.lineDiv.getBoundingClientRect();if(o>l.bottom||!lt(e,r))return dt(t);o-=l.top-a.viewOffset;for(var s=0;s<e.options.gutters.length;++s){var c=a.gutters.childNodes[s];if(c&&c.getBoundingClientRect().right>=i)return it(e,r,e,he(e.doc,o),e.options.gutters[s],t),dt(t)}}function ha(e,t){return fa(e,t,"gutterClick",!0)}function pa(e,t){wr(e.display,t)||function(e,t){if(!lt(e,"gutterContextMenu"))return!1;return fa(e,t,"gutterContextMenu",!1)}(e,t)||ot(e,t,"contextmenu")||_||e.display.input.onContextMenu(t)}function ma(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),qr(e)}ca.prototype.compare=function(e,t,r){return this.time+400>e&&0==ve(t,this.pos)&&r==this.button};var ga={toString:function(){return"CodeMirror.Init"}},va={},ya={};function ba(e){ci(e),Yn(e),xn(e)}function wa(e,t,r){if(!t!=!(r&&r!=ga)){var n=e.display.dragFunctions,i=t?tt:nt;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function xa(e){e.options.lineWrapping?(z(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),Xe(e)),on(e),Yn(e),qr(e),setTimeout(function(){return In(e)},100)}function ka(e,t){var r=this;if(!(this instanceof ka))return new ka(e,t);this.options=t=t?E(t):{},E(va,t,!1),ui(t);var n=t.value;"string"==typeof n?n=new So(n,t.mode,null,t.lineSeparator,t.direction):t.mode&&(n.modeOption=t.mode),this.doc=n;var i=new ka.inputStyles[t.inputStyle](this),o=this.display=new le(e,n,i);for(var a in ci(o.wrapper.CodeMirror=this),ma(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),qn(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:!1,cutIncoming:!1,selectingText:!1,draggingText:!1,highlight:new W,keySeq:null,specialChars:null},t.autofocus&&!f&&o.input.focus(),x&&k<11&&setTimeout(function(){return r.display.input.reset(!0)},20),function(i){var o=i.display;tt(o.scroller,"mousedown",$n(i,ua)),tt(o.scroller,"dblclick",x&&k<11?$n(i,function(e){if(!ot(i,e)){var t=an(i,e);if(t&&!ha(i,e)&&!wr(i.display,e)){ct(e);var r=i.findWordAt(t);Bi(i.doc,r.anchor,r.head)}}}):function(e){return ot(i,e)||ct(e)});tt(o.scroller,"contextmenu",function(e){return pa(i,e)});var r,n={end:0};function a(){o.activeTouch&&(r=setTimeout(function(){return o.activeTouch=null},1e3),(n=o.activeTouch).end=+new Date)}function l(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return 400<r*r+n*n}tt(o.scroller,"touchstart",function(e){if(!ot(i,e)&&!function(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}(e)&&!ha(i,e)){o.input.ensurePolled(),clearTimeout(r);var t=+new Date;o.activeTouch={start:t,moved:!1,prev:t-n.end<=300?n:null},1==e.touches.length&&(o.activeTouch.left=e.touches[0].pageX,o.activeTouch.top=e.touches[0].pageY)}}),tt(o.scroller,"touchmove",function(){o.activeTouch&&(o.activeTouch.moved=!0)}),tt(o.scroller,"touchend",function(e){var t=o.activeTouch;if(t&&!wr(o,e)&&null!=t.left&&!t.moved&&new Date-t.start<300){var r,n=i.coordsChar(o.activeTouch,"page");r=!t.prev||l(t,t.prev)?new vi(n,n):!t.prev.prev||l(t,t.prev.prev)?i.findWordAt(n):new vi(ge(n.line,0),_e(i.doc,ge(n.line+1,0))),i.setSelection(r.anchor,r.head),i.focus(),ct(e)}a()}),tt(o.scroller,"touchcancel",a),tt(o.scroller,"scroll",function(){o.scroller.clientHeight&&(Nn(i,o.scroller.scrollTop),On(i,o.scroller.scrollLeft,!0),it(i,"scroll",i))}),tt(o.scroller,"mousewheel",function(e){return mi(i,e)}),tt(o.scroller,"DOMMouseScroll",function(e){return mi(i,e)}),tt(o.wrapper,"scroll",function(){return o.wrapper.scrollTop=o.wrapper.scrollLeft=0}),o.dragFunctions={enter:function(e){ot(i,e)||ft(e)},over:function(e){ot(i,e)||(!function(e,t){var r=an(e,t);if(r){var n=document.createDocumentFragment();un(e,r,n),e.display.dragCursor||(e.display.dragCursor=A("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),M(e.display.dragCursor,n)}}(i,e),ft(e))},start:function(e){return function(e,t){if(x&&(!e.state.draggingText||+new Date-To<100))ft(t);else if(!ot(e,t)&&!wr(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!s)){var r=A("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",g&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),g&&r.parentNode.removeChild(r)}}(i,e)},drop:$n(i,Lo),leave:function(e){ot(i,e)||Mo(i)}};var e=o.input.getField();tt(e,"keyup",function(e){return oa.call(i,e)}),tt(e,"keydown",$n(i,ia)),tt(e,"keypress",$n(i,aa)),tt(e,"focus",function(e){return gn(i,e)}),tt(e,"blur",function(e){return vn(i,e)})}(this),Oo(),Hn(this),this.curOp.forceUpdate=!0,Ni(this,n),t.autofocus&&!f||this.hasFocus()?setTimeout(P(gn,this),20):vn(this),ya)ya.hasOwnProperty(a)&&ya[a](r,t[a],ga);kn(this),t.finishInit&&t.finishInit(this);for(var l=0;l<_a.length;++l)_a[l](r);Rn(this),b&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}ka.defaults=va,ka.optionHandlers=ya;var _a=[];function Ca(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=Wt(e,t).state:r="prev");var a=e.options.tabSize,l=se(o,t),s=q(l.text,null,a);l.stateAfter&&(l.stateAfter=null);var c,u=l.text.match(/^\s*/)[0];if(n||/\S/.test(l.text)){if("smart"==r&&((c=o.mode.indent(i,l.text.slice(u.length),l.text))==B||150<c)){if(!n)return;r="prev"}}else c=0,r="not";"prev"==r?c=t>o.first?q(se(o,t-1).text,null,a):0:"add"==r?c=s+e.options.indentUnit:"subtract"==r?c=s-e.options.indentUnit:"number"==typeof r&&(c=s+r),c=Math.max(0,c);var d="",f=0;if(e.options.indentWithTabs)for(var h=Math.floor(c/a);h;--h)f+=a,d+="\t";if(f<c&&(d+=G(c-f)),d!=u)return so(o,d,ge(t,0),ge(t,u.length),"+input"),!(l.stateAfter=null);for(var p=0;p<o.sel.ranges.length;p++){var m=o.sel.ranges[p];if(m.head.line==t&&m.head.ch<u.length){var g=ge(t,u.length);Ui(o,p,new vi(g,g));break}}}ka.defineInitHook=function(e){return _a.push(e)};var Sa=null;function Ta(e){Sa=e}function La(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var a,l=e.state.pasteIncoming||"paste"==i,s=xt(t),c=null;if(l&&1<n.ranges.length)if(Sa&&Sa.text.join("\n")==t){if(n.ranges.length%Sa.text.length==0){c=[];for(var u=0;u<Sa.text.length;u++)c.push(o.splitLines(Sa.text[u]))}}else s.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(c=Y(s,function(e){return[e]}));for(var d=n.ranges.length-1;0<=d;d--){var f=n.ranges[d],h=f.from(),p=f.to();f.empty()&&(r&&0<r?h=ge(h.line,h.ch-r):e.state.overwrite&&!l?p=ge(p.line,Math.min(se(o,p.line).text.length,p.ch+X(s).length)):l&&Sa&&Sa.lineWise&&Sa.text.join("\n")==t&&(h=p=ge(h.line,0))),a=e.curOp.updateInput;var m={from:h,to:p,text:c?c[d%c.length]:s,origin:i||(l?"paste":e.state.cutIncoming?"cut":"+input")};no(e.doc,m),lr(e,"inputRead",e,m)}t&&!l&&Na(e,t),Sn(e),e.curOp.updateInput=a,e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=!1}function Ma(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||Vn(t,function(){return La(t,r,0,null,"paste")}),!0}function Na(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;0<=n;n--){var i=r.ranges[n];if(!(100<i.head.ch||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var l=0;l<o.electricChars.length;l++)if(-1<t.indexOf(o.electricChars.charAt(l))){a=Ca(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(se(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=Ca(e,i.head.line,"smart"));a&&lr(e,"electricInput",e,i.head.line)}}}function Aa(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:ge(i,0),head:ge(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function Oa(e,t){e.setAttribute("autocorrect","off"),e.setAttribute("autocapitalize","off"),e.setAttribute("spellcheck",!!t)}function Da(){var e=A("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=A("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return b?e.style.width="1000px":e.setAttribute("wrap","off"),u&&(e.style.border="1px solid black"),Oa(e),t}function za(n,i,o,e,a){var t=i,r=o,l=se(n,i.line);function s(e){var t,r;if(null==(t=a?function(t,r,l,e){var s=Je(r,t.doc.direction);if(!s)return $o(r,l,e);l.ch>=r.text.length?(l.ch=r.text.length,l.sticky="before"):l.ch<=0&&(l.ch=0,l.sticky="after");var n=Ze(s,l.ch,l.sticky),i=s[n];if("ltr"==t.doc.direction&&i.level%2==0&&(0<e?i.to>l.ch:i.from<l.ch))return $o(r,l,e);var o,c=function(e,t){return Vo(r,e instanceof ge?e.ch:e,t)},a=function(e){return t.options.lineWrapping?(o=o||Ar(t,r),Yr(t,r,o,e)):{begin:0,end:r.text.length}},u=a("before"==l.sticky?c(l,-1):l.ch);if("rtl"==t.doc.direction||1==i.level){var d=1==i.level==e<0,f=c(l,d?1:-1);if(null!=f&&(d?f<=i.to&&f<=u.end:f>=i.from&&f>=u.begin)){var h=d?"before":"after";return new ge(l.line,f,h)}}var p=function(e,t,r){for(var n=function(e,t){return t?new ge(l.line,c(e,1),"before"):new ge(l.line,e,"after")};0<=e&&e<s.length;e+=t){var i=s[e],o=0<t==(1!=i.level),a=o?r.begin:c(r.end,-1);if(i.from<=a&&a<i.to)return n(a,o);if(a=o?i.from:c(i.to,-1),r.begin<=a&&a<r.end)return n(a,o)}},m=p(n+e,e,u);if(m)return m;var g=0<e?u.end:c(u.begin,-1);return null==g||0<e&&g==r.text.length||!(m=p(0<e?0:s.length-1,e,a(g)))?null:m}(n.cm,l,i,o):$o(l,i,o))){if(e||(r=i.line+o)<n.first||r>=n.first+n.size||(i=new ge(r,i.ch,i.sticky),!(l=se(n,r))))return!1;i=Go(a,n.cm,l,i.line,o)}else i=t;return!0}if("char"==e)s();else if("column"==e)s(!0);else if("word"==e||"group"==e)for(var c=null,u="group"==e,d=n.cm&&n.cm.getHelper(i,"wordChars"),f=!0;!(o<0)||s(!f);f=!1){var h=l.text.charAt(i.ch)||"\n",p=te(h,d)?"w":u&&"\n"==h?"n":!u||/\s/.test(h)?null:"p";if(!u||f||p||(p="s"),c&&c!=p){o<0&&(o=1,s(),i.sticky="after");break}if(p&&(c=p),0<o&&!s(!f))break}var m=Ji(n,i,t,r,!0);return ye(t,m)&&(m.hitSide=!0),m}function Fa(e,t,r,n){var i,o,a=e.doc,l=t.left;if("page"==n){var s=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),c=Math.max(s-.5*Jr(e.display),3);i=(0<r?t.bottom:t.top)+r*c}else"line"==n&&(i=0<r?t.bottom+3:t.top-3);for(;(o=Gr(e,l,i)).outside;){if(r<0?i<=0:i>=a.height){o.hitSide=!0;break}i+=5*r}return o}var Ia=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new W,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Pa(e,t){var r=Nr(e,t.line);if(!r||r.hidden)return null;var n=se(e.doc,t.line),i=Lr(r,n,t.line),o=Je(n,e.doc.direction),a="left";o&&(a=Ze(o,t.ch)%2?"right":"left");var l=Fr(i.map,t.ch,a);return l.offset="right"==l.collapse?l.end:l.start,l}function Ea(e,t){return t&&(e.bad=!0),e}function qa(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return Ea(e.clipPos(ge(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return Wa(o,t,r)}}function Wa(c,e,t){var r=c.text.firstChild,n=!1;if(!e||!O(r,e))return Ea(ge(fe(c.line),0),!0);if(e==r&&(n=!0,e=r.childNodes[t],t=0,!e)){var i=c.rest?X(c.rest):c.line;return Ea(ge(fe(i),i.text.length),n)}var o=3==e.nodeType?e:null,a=e;for(o||1!=e.childNodes.length||3!=e.firstChild.nodeType||(o=e.firstChild,t&&(t=o.nodeValue.length));a.parentNode!=r;)a=a.parentNode;var u=c.measure,d=u.maps;function l(e,t,r){for(var n=-1;n<(d?d.length:0);n++)for(var i=n<0?u.map:d[n],o=0;o<i.length;o+=3){var a=i[o+2];if(a==e||a==t){var l=fe(n<0?c.line:c.rest[n]),s=i[o]+r;return(r<0||a!=e)&&(s=i[o+(r?1:0)]),ge(l,s)}}}var s=l(o,a,t);if(s)return Ea(s,n);for(var f=a.nextSibling,h=o?o.nodeValue.length-t:0;f;f=f.nextSibling){if(s=l(f,f.firstChild,0))return Ea(ge(s.line,s.ch-h),n);h+=f.textContent.length}for(var p=a.previousSibling,m=t;p;p=p.previousSibling){if(s=l(p,p.firstChild,-1))return Ea(ge(s.line,s.ch+m),n);m+=p.textContent.length}}Ia.prototype.init=function(e){var t=this,a=this,l=a.cm,s=a.div=e.lineDiv;function r(e){if(!ot(l,e)){if(l.somethingSelected())Ta({lineWise:!1,text:l.getSelections()}),"cut"==e.type&&l.replaceSelection("",null,"cut");else{if(!l.options.lineWiseCopyCut)return;var t=Aa(l);Ta({lineWise:!0,text:t.text}),"cut"==e.type&&l.operation(function(){l.setSelections(t.ranges,0,j),l.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var r=Sa.text.join("\n");if(e.clipboardData.setData("Text",r),e.clipboardData.getData("Text")==r)return void e.preventDefault()}var n=Da(),i=n.firstChild;l.display.lineSpace.insertBefore(n,l.display.lineSpace.firstChild),i.value=Sa.text.join("\n");var o=document.activeElement;I(i),setTimeout(function(){l.display.lineSpace.removeChild(n),o.focus(),o==s&&a.showPrimarySelection()},50)}}Oa(s,l.options.spellcheck),tt(s,"paste",function(e){ot(l,e)||Ma(e,l)||k<=11&&setTimeout($n(l,function(){return t.updateFromDOM()}),20)}),tt(s,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),tt(s,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),tt(s,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),tt(s,"touchstart",function(){return a.forceCompositionEnd()}),tt(s,"input",function(){t.composing||t.readFromDOMSoon()}),tt(s,"copy",r),tt(s,"cut",r)},Ia.prototype.prepareSelection=function(){var e=cn(this.cm,!1);return e.focus=this.cm.state.focused,e},Ia.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Ia.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Ia.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,r=t.doc.sel.primary(),n=r.from(),i=r.to();if(t.display.viewTo==t.display.viewFrom||n.line>=t.display.viewTo||i.line<t.display.viewFrom)e.removeAllRanges();else{var o=qa(t,e.anchorNode,e.anchorOffset),a=qa(t,e.focusNode,e.focusOffset);if(!o||o.bad||!a||a.bad||0!=ve(xe(o,a),n)||0!=ve(we(o,a),i)){var l=t.display.view,s=n.line>=t.display.viewFrom&&Pa(t,n)||{node:l[0].measure.map[2],offset:0},c=i.line<t.display.viewTo&&Pa(t,i);if(!c){var u=l[l.length-1].measure,d=u.maps?u.maps[u.maps.length-1]:u.map;c={node:d[d.length-1],offset:d[d.length-2]-d[d.length-3]}}if(s&&c){var f,h=e.rangeCount&&e.getRangeAt(0);try{f=S(s.node,s.offset,c.offset,c.node)}catch(e){}f&&(!m&&t.state.focused?(e.collapse(s.node,s.offset),f.collapsed||(e.removeAllRanges(),e.addRange(f))):(e.removeAllRanges(),e.addRange(f)),h&&null==e.anchorNode?e.addRange(h):m&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Ia.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Ia.prototype.showMultipleSelections=function(e){M(this.cm.display.cursorDiv,e.cursors),M(this.cm.display.selectionDiv,e.selection)},Ia.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Ia.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return O(this.div,t)},Ia.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Ia.prototype.blur=function(){this.div.blur()},Ia.prototype.getField=function(){return this.div},Ia.prototype.supportsTouch=function(){return!0},Ia.prototype.receivedFocus=function(){var t=this;this.selectionInEditor()?this.pollSelection():Vn(this.cm,function(){return t.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,function e(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,e))})},Ia.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Ia.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(d&&a&&this.cm.options.gutters.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=qa(t,e.anchorNode,e.anchorOffset),n=qa(t,e.focusNode,e.focusOffset);r&&n&&Vn(t,function(){$i(t.doc,bi(r,n),j),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)})}}},Ia.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),a=o.from(),l=o.to();if(0==a.ch&&a.line>n.firstLine()&&(a=ge(a.line-1,se(n.doc,a.line-1).length)),l.ch==se(n.doc,l.line).text.length&&l.line<n.lastLine()&&(l=ge(l.line+1,0)),a.line<i.viewFrom||l.line>i.viewTo-1)return!1;a.line==i.viewFrom||0==(e=ln(n,a.line))?(t=fe(i.view[0].line),r=i.view[0].node):(t=fe(i.view[e].line),r=i.view[e-1].node.nextSibling);var s,c,u=ln(n,l.line);if(u==i.view.length-1?(s=i.viewTo-1,c=i.lineDiv.lastChild):(s=fe(i.view[u+1].line)-1,c=i.view[u+1].node.previousSibling),!r)return!1;for(var d=n.doc.splitLines(function(s,e,t,c,u){var r="",d=!1,f=s.doc.lineSeparator(),h=!1;function p(){d&&(r+=f,h&&(r+=f),d=h=!1)}function m(e){e&&(p(),r+=e)}function g(e){if(1==e.nodeType){var t=e.getAttribute("cm-text");if(t)return void m(t);var r,n=e.getAttribute("cm-marker");if(n){var i=s.findMarks(ge(c,0),ge(u+1,0),(l=+n,function(e){return e.id==l}));return void(i.length&&(r=i[0].find(0))&&m(ce(s.doc,r.from,r.to).join(f)))}if("false"==e.getAttribute("contenteditable"))return;var o=/^(pre|div|p|li|table|br)$/i.test(e.nodeName);if(!/^br$/i.test(e.nodeName)&&0==e.textContent.length)return;o&&p();for(var a=0;a<e.childNodes.length;a++)g(e.childNodes[a]);/^(pre|p)$/i.test(e.nodeName)&&(h=!0),o&&(d=!0)}else 3==e.nodeType&&m(e.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "));var l}for(;g(e),e!=t;)e=e.nextSibling,h=!1;return r}(n,r,c,t,s)),f=ce(n.doc,ge(t,0),ge(s,se(n.doc,s).text.length));1<d.length&&1<f.length;)if(X(d)==X(f))d.pop(),f.pop(),s--;else{if(d[0]!=f[0])break;d.shift(),f.shift(),t++}for(var h=0,p=0,m=d[0],g=f[0],v=Math.min(m.length,g.length);h<v&&m.charCodeAt(h)==g.charCodeAt(h);)++h;for(var y=X(d),b=X(f),w=Math.min(y.length-(1==d.length?h:0),b.length-(1==f.length?h:0));p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==d.length&&1==f.length&&t==a.line)for(;h&&h>a.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)h--,p++;d[d.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),d[0]=d[0].slice(h).replace(/\u200b+$/,"");var x=ge(t,h),k=ge(s,f.length?X(f).length-p:0);return 1<d.length||d[0]||ve(x,k)?(so(n.doc,d,x,k,"+input"),!0):void 0},Ia.prototype.ensurePolled=function(){this.forceCompositionEnd()},Ia.prototype.reset=function(){this.forceCompositionEnd()},Ia.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Ia.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},Ia.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Vn(this.cm,function(){return Yn(e.cm)})},Ia.prototype.setUneditable=function(e){e.contentEditable="false"},Ia.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||$n(this.cm,La)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Ia.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Ia.prototype.onContextMenu=function(){},Ia.prototype.resetPosition=function(){},Ia.prototype.needsContentAttribute=!0;var Ha,Ra,Ba,ja=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new W,this.hasSelection=!1,this.composing=null};ja.prototype.init=function(t){var e=this,r=this,n=this.cm;this.createField(t);var i=this.textarea;function o(e){if(!ot(n,e)){if(n.somethingSelected())Ta({lineWise:!1,text:n.getSelections()});else{if(!n.options.lineWiseCopyCut)return;var t=Aa(n);Ta({lineWise:!0,text:t.text}),"cut"==e.type?n.setSelections(t.ranges,null,j):(r.prevInput="",i.value=t.text.join("\n"),I(i))}"cut"==e.type&&(n.state.cutIncoming=!0)}}t.wrapper.insertBefore(this.wrapper,t.wrapper.firstChild),u&&(i.style.width="0px"),tt(i,"input",function(){x&&9<=k&&e.hasSelection&&(e.hasSelection=null),r.poll()}),tt(i,"paste",function(e){ot(n,e)||Ma(e,n)||(n.state.pasteIncoming=!0,r.fastPoll())}),tt(i,"cut",o),tt(i,"copy",o),tt(t.scroller,"paste",function(e){wr(t,e)||ot(n,e)||(n.state.pasteIncoming=!0,r.focus())}),tt(t.lineSpace,"selectstart",function(e){wr(t,e)||ct(e)}),tt(i,"compositionstart",function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}}),tt(i,"compositionend",function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)})},ja.prototype.createField=function(e){this.wrapper=Da(),this.textarea=this.wrapper.firstChild},ja.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=cn(e);if(e.options.moveInputWithCursor){var i=Kr(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return n},ja.prototype.showSelection=function(e){var t=this.cm.display;M(t.cursorDiv,e.cursors),M(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},ja.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&I(this.textarea),x&&9<=k&&(this.hasSelection=r)}else e||(this.prevInput=this.textarea.value="",x&&9<=k&&(this.hasSelection=null))}},ja.prototype.getField=function(){return this.textarea},ja.prototype.supportsTouch=function(){return!1},ja.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!f||D()!=this.textarea))try{this.textarea.focus()}catch(e){}},ja.prototype.blur=function(){this.textarea.blur()},ja.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},ja.prototype.receivedFocus=function(){this.slowPoll()},ja.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},ja.prototype.fastPoll=function(){var t=!1,r=this;r.pollingFast=!0,r.polling.set(20,function e(){r.poll()||t?(r.pollingFast=!1,r.slowPoll()):(t=!0,r.polling.set(60,e))})},ja.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||!t.state.focused||kt(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(x&&9<=k&&this.hasSelection===i||w&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var a=0,l=Math.min(n.length,i.length);a<l&&n.charCodeAt(a)==i.charCodeAt(a);)++a;return Vn(t,function(){La(t,i.slice(a),n.length-a,null,e.composing?"*compose":null),1e3<i.length||-1<i.indexOf("\n")?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},ja.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},ja.prototype.onKeyPress=function(){x&&9<=k&&(this.hasSelection=null),this.fastPoll()},ja.prototype.onContextMenu=function(e){var r=this,n=r.cm,i=n.display,o=r.textarea,t=an(n,e),a=i.scroller.scrollTop;if(t&&!g){n.options.resetSelectionOnContextMenu&&-1==n.doc.sel.contains(t)&&$n(n,$i)(n.doc,bi(t),j);var l=o.style.cssText,s=r.wrapper.style.cssText;r.wrapper.style.cssText="position: absolute";var c,u=r.wrapper.getBoundingClientRect();if(o.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-u.top-5)+"px; left: "+(e.clientX-u.left-5)+"px;\n      z-index: 1000; background: "+(x?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",b&&(c=window.scrollY),i.input.focus(),b&&window.scrollTo(null,c),i.input.reset(),n.somethingSelected()||(o.value=r.prevInput=" "),r.contextMenuPending=!0,i.selForContextMenu=n.doc.sel,clearTimeout(i.detectingSelectAll),x&&9<=k&&f(),_){ft(e);var d=function(){nt(window,"mouseup",d),setTimeout(h,20)};tt(window,"mouseup",d)}else setTimeout(h,50)}function f(){if(null!=o.selectionStart){var e=n.somethingSelected(),t="​"+(e?o.value:"");o.value="⇚",o.value=t,r.prevInput=e?"":"​",o.selectionStart=1,o.selectionEnd=t.length,i.selForContextMenu=n.doc.sel}}function h(){if(r.contextMenuPending=!1,r.wrapper.style.cssText=s,o.style.cssText=l,x&&k<9&&i.scrollbars.setScrollTop(i.scroller.scrollTop=a),null!=o.selectionStart){(!x||x&&k<9)&&f();var e=0,t=function(){i.selForContextMenu==n.doc.sel&&0==o.selectionStart&&0<o.selectionEnd&&"​"==r.prevInput?$n(n,to)(n):e++<10?i.detectingSelectAll=setTimeout(t,500):(i.selForContextMenu=null,i.input.reset())};i.detectingSelectAll=setTimeout(t,200)}}},ja.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e},ja.prototype.setUneditable=function(){},ja.prototype.needsContentAttribute=!1,function(i){var o=i.optionHandlers;function e(e,t,n,r){i.defaults[e]=t,n&&(o[e]=r?function(e,t,r){r!=ga&&n(e,t,r)}:n)}i.defineOption=e,i.Init=ga,e("value","",function(e,t){return e.setValue(t)},!0),e("mode",null,function(e,t){e.doc.modeOption=t,Ci(e)},!0),e("indentUnit",2,Ci,!0),e("indentWithTabs",!1),e("smartIndent",!0),e("tabSize",4,function(e){Si(e),qr(e),Yn(e)},!0),e("lineSeparator",null,function(e,n){if(e.doc.lineSep=n){var i=[],o=e.doc.first;e.doc.iter(function(e){for(var t=0;;){var r=e.text.indexOf(n,t);if(-1==r)break;t=r+n.length,i.push(ge(o,r))}o++});for(var t=i.length-1;0<=t;t--)so(e.doc,n,i[t],ge(i[t].line,i[t].ch+n.length))}}),e("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff]/g,function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=ga&&e.refresh()}),e("specialCharPlaceholder",Qt,function(e){return e.refresh()},!0),e("electricChars",!0),e("inputStyle",f?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),e("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),e("rtlMoveVisually",!p),e("wholeLineUpdateBefore",!0),e("theme","default",function(e){ma(e),ba(e)},!0),e("keyMap","default",function(e,t,r){var n=Uo(t),i=r!=ga&&Uo(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)}),e("extraKeys",null),e("configureMouse",null),e("lineWrapping",!1,xa,!0),e("gutters",[],function(e){ui(e.options),ba(e)},!0),e("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?rn(e.display)+"px":"0",e.refresh()},!0),e("coverGutterNextToScrollbar",!1,function(e){return In(e)},!0),e("scrollbarStyle","native",function(e){qn(e),In(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),e("lineNumbers",!1,function(e){ui(e.options),ba(e)},!0),e("firstLineNumber",1,ba,!0),e("lineNumberFormatter",function(e){return e},ba,!0),e("showCursorWhenSelecting",!1,sn,!0),e("resetSelectionOnContextMenu",!0),e("lineWiseCopyCut",!0),e("pasteLinesPerSelection",!0),e("selectionsMayTouch",!1),e("readOnly",!1,function(e,t){"nocursor"==t&&(vn(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),e("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),e("dragDrop",!0,wa),e("allowDropFileTypes",null),e("cursorBlinkRate",530),e("cursorScrollMargin",0),e("cursorHeight",1,sn,!0),e("singleCursorHeightPerLine",!0,sn,!0),e("workTime",100),e("workDelay",100),e("flattenSpans",!0,Si,!0),e("addModeClass",!1,Si,!0),e("pollInterval",100),e("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),e("historyEventDelay",1250),e("viewportMargin",10,function(e){return e.refresh()},!0),e("maxHighlightLength",1e4,Si,!0),e("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),e("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),e("autofocus",null),e("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0),e("phrases",null)}(ka),Ra=(Ha=ka).optionHandlers,Ba=Ha.helpers={},Ha.prototype={constructor:Ha,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,t){var r=this.options,n=r[e];r[e]==t&&"mode"!=e||(r[e]=t,Ra.hasOwnProperty(e)&&$n(this,Ra[e])(this,t,n),it(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Uo(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:Gn(function(e,t){var r=e.token?e:Ha.getMode(this.options,e);if(r.startState)throw new Error("Overlays may not be stateful.");!function(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}(this.state.overlays,{mode:r,modeSpec:e,opaque:t&&t.opaque,priority:t&&t.priority||0},function(e){return e.priority}),this.state.modeGen++,Yn(this)}),removeOverlay:Gn(function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e)return t.splice(r,1),this.state.modeGen++,void Yn(this)}}),indentLine:Gn(function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),pe(this.doc,e)&&Ca(this,e,t,r)}),indentSelection:Gn(function(e){for(var t=this,r=this.doc.sel.ranges,n=-1,i=0;i<r.length;i++){var o=r[i];if(o.empty())o.head.line>n&&(Ca(t,o.head.line,e,!0),n=o.head.line,i==t.doc.sel.primIndex&&Sn(t));else{var a=o.from(),l=o.to(),s=Math.max(n,a.line);n=Math.min(t.lastLine(),l.line-(l.ch?0:1))+1;for(var c=s;c<n;++c)Ca(t,c,e);var u=t.doc.sel.ranges;0==a.ch&&r.length==u.length&&0<u[i].from().ch&&Ui(t.doc,i,new vi(a,u[i].to()),j)}}}),getTokenAt:function(e,t){return Ut(this,e,t)},getLineTokens:function(e,t){return Ut(this,ge(e),t,!0)},getTokenTypeAt:function(e){e=_e(this.doc,e);var t,r=qt(this,se(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var a=n+i>>1;if((a?r[2*a-1]:0)>=o)i=a;else{if(!(r[2*a+1]<o)){t=r[2*a+2];break}n=a+1}}var l=t?t.indexOf("overlay "):-1;return l<0?t:0==l?null:t.slice(0,l-1)},getModeAt:function(e){var t=this.doc.mode;return t.innerMode?Ha.innerMode(t,this.getTokenAt(e).state).mode:t},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var r=[];if(!Ba.hasOwnProperty(t))return r;var n=Ba[t],i=this.getModeAt(e);if("string"==typeof i[t])n[i[t]]&&r.push(n[i[t]]);else if(i[t])for(var o=0;o<i[t].length;o++){var a=n[i[t][o]];a&&r.push(a)}else i.helperType&&n[i.helperType]?r.push(n[i.helperType]):n[i.name]&&r.push(n[i.name]);for(var l=0;l<n._global.length;l++){var s=n._global[l];s.pred(i,this)&&-1==H(r,s.val)&&r.push(s.val)}return r},getStateAfter:function(e,t){var r=this.doc;return Wt(this,(e=ke(r,null==e?r.first+r.size-1:e))+1,t).state},cursorCoords:function(e,t){var r=this.doc.sel.primary();return Kr(this,null==e?r.head:"object"==typeof e?_e(this.doc,e):e?r.from():r.to(),t||"page")},charCoords:function(e,t){return Ur(this,_e(this.doc,e),t||"page")},coordsChar:function(e,t){return Gr(this,(e=jr(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=jr(this,{top:e,left:0},t||"page").top,he(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:o<e&&(e=o,i=!0),n=se(this.doc,e)}else n=e;return Br(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-$e(n):0)},defaultTextHeight:function(){return Jr(this.display)},defaultCharWidth:function(){return en(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o,a,l,s=this.display,c=(e=Kr(this,_e(this.doc,e))).bottom,u=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),s.sizer.appendChild(t),"over"==n)c=e.top;else if("above"==n||"near"==n){var d=Math.max(s.wrapper.clientHeight,this.doc.height),f=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>d)&&e.top>t.offsetHeight?c=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=d&&(c=e.bottom),u+t.offsetWidth>f&&(u=f-t.offsetWidth)}t.style.top=c+"px",t.style.left=t.style.right="","right"==i?(u=s.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?u=0:"middle"==i&&(u=(s.sizer.clientWidth-t.offsetWidth)/2),t.style.left=u+"px"),r&&(o=this,a={left:u,top:c,right:u+t.offsetWidth,bottom:c+t.offsetHeight},null!=(l=_n(o,a)).scrollTop&&Nn(o,l.scrollTop),null!=l.scrollLeft&&On(o,l.scrollLeft))},triggerOnKeyDown:Gn(ia),triggerOnKeyPress:Gn(aa),triggerOnKeyUp:oa,triggerOnMouseDown:Gn(ua),execCommand:function(e){if(Xo.hasOwnProperty(e))return Xo[e].call(null,this)},triggerElectric:Gn(function(e){Na(this,e)}),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=_e(this.doc,e),a=0;a<t&&!(o=za(this.doc,o,i,r,n)).hitSide;++a);return o},moveH:Gn(function(t,r){var n=this;this.extendSelectionsBy(function(e){return n.display.shift||n.doc.extend||e.empty()?za(n.doc,e.head,t,r,n.options.rtlMoveVisually):t<0?e.from():e.to()},K)}),deleteH:Gn(function(r,n){var e=this.doc.sel,i=this.doc;e.somethingSelected()?i.replaceSelection("",null,"+delete"):Ko(this,function(e){var t=za(i,e.head,r,n,!1);return r<0?{from:t,to:e.head}:{from:e.head,to:t}})}),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var a=_e(this.doc,e),l=0;l<t;++l){var s=Kr(this,a,"div");if(null==o?o=s.left:s.left=o,(a=Fa(this,s,i,r)).hitSide)break}return a},moveV:Gn(function(n,i){var o=this,a=this.doc,l=[],s=!this.display.shift&&!a.extend&&a.sel.somethingSelected();if(a.extendSelectionsBy(function(e){if(s)return n<0?e.from():e.to();var t=Kr(o,e.head,"div");null!=e.goalColumn&&(t.left=e.goalColumn),l.push(t.left);var r=Fa(o,t,n,i);return"page"==i&&e==a.sel.primary()&&Cn(o,Ur(o,r,"div").top-t.top),r},K),l.length)for(var e=0;e<a.sel.ranges.length;e++)a.sel.ranges[e].goalColumn=l[e]}),findWordAt:function(e){var t=se(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&n!=t.length||!r?++n:--r;for(var o=t.charAt(r),a=te(o,i)?function(e){return te(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!te(e)};0<r&&a(t.charAt(r-1));)--r;for(;n<t.length&&a(t.charAt(n));)++n}return new vi(ge(e.line,r),ge(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?z(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),it(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==D()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Gn(function(e,t){Tn(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Cr(this)-this.display.barHeight,width:e.scrollWidth-Cr(this)-this.display.barWidth,clientHeight:Tr(this),clientWidth:Sr(this)}},scrollIntoView:Gn(function(e,t){var r,n;null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:ge(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?(n=e,Ln(r=this),r.curOp.scrollToPos=n):Mn(this,e.from,e.to,e.margin)}),setSize:Gn(function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&Er(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Zn(r,i,"widget");break}++i}),this.curOp.forceUpdate=!0,it(this,"refresh",this)}),operation:function(e){return Vn(this,e)},startOperation:function(){return Hn(this)},endOperation:function(){return Rn(this)},refresh:Gn(function(){var e=this.display.cachedTextHeight;Yn(this),this.curOp.forceUpdate=!0,qr(this),Tn(this,this.doc.scrollLeft,this.doc.scrollTop),li(this),(null==e||.5<Math.abs(e-Jr(this.display)))&&on(this),it(this,"refresh",this)}),swapDoc:Gn(function(e){var t=this.doc;return t.cm=null,Ni(this,e),qr(this),this.display.input.reset(),Tn(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,lr(this,"swapDoc",this,t),t}),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},st(Ha),Ha.registerHelper=function(e,t,r){Ba.hasOwnProperty(e)||(Ba[e]=Ha[e]={_global:[]}),Ba[e][t]=r},Ha.registerGlobalHelper=function(e,t,r,n){Ha.registerHelper(e,t,n),Ba[e]._global.push({pred:r,val:n})};var Ua,Ka="iter insert remove copy getEditor constructor".split(" ");for(var Va in So.prototype)So.prototype.hasOwnProperty(Va)&&H(Ka,Va)<0&&(ka.prototype[Va]=function(e){return function(){return e.apply(this.doc,arguments)}}(So.prototype[Va]));return st(So),ka.inputStyles={textarea:ja,contenteditable:Ia},ka.defineMode=function(e){ka.defaults.mode||"null"==e||(ka.defaults.mode=e),function(e,t){2<arguments.length&&(t.dependencies=Array.prototype.slice.call(arguments,2)),St[e]=t}.apply(this,arguments)},ka.defineMIME=function(e,t){Tt[e]=t},ka.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),ka.defineMIME("text/plain","null"),ka.defineExtension=function(e,t){ka.prototype[e]=t},ka.defineDocExtension=function(e,t){So.prototype[e]=t},ka.fromTextArea=function(t,e){if((e=e?E(e):{}).value=t.value,!e.tabindex&&t.tabIndex&&(e.tabindex=t.tabIndex),!e.placeholder&&t.placeholder&&(e.placeholder=t.placeholder),null==e.autofocus){var r=D();e.autofocus=r==t||null!=t.getAttribute("autofocus")&&r==document.body}function n(){t.value=l.getValue()}var i;if(t.form&&(tt(t.form,"submit",n),!e.leaveSubmitMethodAlone)){var o=t.form;i=o.submit;try{var a=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=a}}catch(e){}}e.finishInit=function(e){e.save=n,e.getTextArea=function(){return t},e.toTextArea=function(){e.toTextArea=isNaN,n(),t.parentNode.removeChild(e.getWrapperElement()),t.style.display="",t.form&&(nt(t.form,"submit",n),"function"==typeof t.form.submit&&(t.form.submit=i))}},t.style.display="none";var l=ka(function(e){return t.parentNode.insertBefore(e,t.nextSibling)},e);return l},(Ua=ka).off=nt,Ua.on=tt,Ua.wheelEventPixels=pi,Ua.Doc=So,Ua.splitLines=xt,Ua.countColumn=q,Ua.findColumn=V,Ua.isWordChar=ee,Ua.Pass=B,Ua.signal=it,Ua.Line=$t,Ua.changeEnd=wi,Ua.scrollbarModel=En,Ua.Pos=ge,Ua.cmpPos=ve,Ua.modes=St,Ua.mimeModes=Tt,Ua.resolveMode=Lt,Ua.getMode=Mt,Ua.modeExtensions=Nt,Ua.extendMode=At,Ua.copyState=Ot,Ua.startState=zt,Ua.innerMode=Dt,Ua.commands=Xo,Ua.keyMap=Eo,Ua.keyName=jo,Ua.isModifierKey=Ro,Ua.lookupKey=Ho,Ua.normalizeKeyMap=Wo,Ua.StringStream=Ft,Ua.SharedTextMarker=xo,Ua.TextMarker=bo,Ua.LineWidget=go,Ua.e_preventDefault=ct,Ua.e_stopPropagation=ut,Ua.e_stop=ft,Ua.addClass=z,Ua.contains=O,Ua.rmClass=T,Ua.keyNames=zo,ka.version="5.42.0",ka}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../fold/xml-fold")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","../fold/xml-fold"],e):e(CodeMirror)}(function(a){"use strict";function l(e){e.state.tagHit&&e.state.tagHit.clear(),e.state.tagOther&&e.state.tagOther.clear(),e.state.tagHit=e.state.tagOther=null}function n(o){o.state.failedTagMatch=!1,o.operation(function(){if(l(o),!o.somethingSelected()){var e=o.getCursor(),t=o.getViewport();t.from=Math.min(t.from,e.line),t.to=Math.max(e.line+1,t.to);var r=a.findMatchingTag(o,e,t);if(r){if(o.state.matchBothTags){var n="open"==r.at?r.open:r.close;n&&(o.state.tagHit=o.markText(n.from,n.to,{className:"CodeMirror-matchingtag"}))}var i="close"==r.at?r.open:r.close;i?o.state.tagOther=o.markText(i.from,i.to,{className:"CodeMirror-matchingtag"}):o.state.failedTagMatch=!0}}})}function i(e){e.state.failedTagMatch&&n(e)}a.defineOption("matchTags",!1,function(e,t,r){r&&r!=a.Init&&(e.off("cursorActivity",n),e.off("viewportChange",i),l(e)),t&&(e.state.matchBothTags="object"==typeof t&&t.bothTags,e.on("cursorActivity",n),e.on("viewportChange",i),n(e))}),a.commands.toMatchingTag=function(e){var t=a.findMatchingTag(e,e.getCursor());if(t){var r="close"==t.at?t.open:t.close;r&&e.extendSelection(r.to,r.from)}}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(i){"use strict";var l="CodeMirror-activeline",s="CodeMirror-activeline-background",c="CodeMirror-activeline-gutter";function u(e){for(var t=0;t<e.state.activeLines.length;t++)e.removeLineClass(e.state.activeLines[t],"wrap",l),e.removeLineClass(e.state.activeLines[t],"background",s),e.removeLineClass(e.state.activeLines[t],"gutter",c)}function o(t,e){for(var r=[],n=0;n<e.length;n++){var i=e[n],o=t.getOption("styleActiveLine");if("object"==typeof o&&o.nonEmpty?i.anchor.line==i.head.line:i.empty()){var a=t.getLineHandleVisualStart(i.head.line);r[r.length-1]!=a&&r.push(a)}}(function(e,t){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!=t[r])return!1;return!0})(t.state.activeLines,r)||t.operation(function(){u(t);for(var e=0;e<r.length;e++)t.addLineClass(r[e],"wrap",l),t.addLineClass(r[e],"background",s),t.addLineClass(r[e],"gutter",c);t.state.activeLines=r})}function a(e,t){o(e,t.ranges)}i.defineOption("styleActiveLine",!1,function(e,t,r){var n=r!=i.Init&&r;t!=n&&(n&&(e.off("beforeSelectionChange",a),u(e),delete e.state.activeLines),t&&(e.state.activeLines=[],o(e,e.listSelections()),e.on("beforeSelectionChange",a)))})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";var s=e.Pos;function c(e,t){return e.line-t.line||e.ch-t.ch}var t="A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",n=new RegExp("<(/?)(["+t+"][A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD-:.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*)","g");function u(e,t,r,n){this.line=t,this.ch=r,this.cm=e,this.text=e.getLine(t),this.min=n?Math.max(n.from,e.firstLine()):e.firstLine(),this.max=n?Math.min(n.to-1,e.lastLine()):e.lastLine()}function i(e,t){var r=e.cm.getTokenTypeAt(s(e.line,t));return r&&/\btag\b/.test(r)}function o(e){if(!(e.line>=e.max))return e.ch=0,e.text=e.cm.getLine(++e.line),!0}function a(e){if(!(e.line<=e.min))return e.text=e.cm.getLine(--e.line),e.ch=e.text.length,!0}function d(e){for(;;){var t=e.text.indexOf(">",e.ch);if(-1==t){if(o(e))continue;return}if(i(e,t+1)){var r=e.text.lastIndexOf("/",t),n=-1<r&&!/\S/.test(e.text.slice(r+1,t));return e.ch=t+1,n?"selfClose":"regular"}e.ch=t+1}}function f(e){for(;;){var t=e.ch?e.text.lastIndexOf("<",e.ch-1):-1;if(-1==t){if(a(e))continue;return}if(i(e,t+1)){n.lastIndex=t,e.ch=t;var r=n.exec(e.text);if(r&&r.index==t)return r}else e.ch=t}}function h(e){for(;;){n.lastIndex=e.ch;var t=n.exec(e.text);if(!t){if(o(e))continue;return}if(i(e,t.index+1))return e.ch=t.index+t[0].length,t;e.ch=t.index+1}}function p(e){for(;;){var t=e.ch?e.text.lastIndexOf(">",e.ch-1):-1;if(-1==t){if(a(e))continue;return}if(i(e,t+1)){var r=e.text.lastIndexOf("/",t),n=-1<r&&!/\S/.test(e.text.slice(r+1,t));return e.ch=t+1,n?"selfClose":"regular"}e.ch=t}}function m(e,t){for(var r=[];;){var n,i=h(e),o=e.line,a=e.ch-(i?i[0].length:0);if(!i||!(n=d(e)))return;if("selfClose"!=n)if(i[1]){for(var l=r.length-1;0<=l;--l)if(r[l]==i[2]){r.length=l;break}if(l<0&&(!t||t==i[2]))return{tag:i[2],from:s(o,a),to:s(e.line,e.ch)}}else r.push(i[2])}}function g(e,t){for(var r=[];;){var n=p(e);if(!n)return;if("selfClose"!=n){var i=e.line,o=e.ch,a=f(e);if(!a)return;if(a[1])r.push(a[2]);else{for(var l=r.length-1;0<=l;--l)if(r[l]==a[2]){r.length=l;break}if(l<0&&(!t||t==a[2]))return{tag:a[2],from:s(e.line,e.ch),to:s(i,o)}}}else f(e)}}e.registerHelper("fold","xml",function(e,t){for(var r=new u(e,t.line,0);;){var n=h(r);if(!n||r.line!=t.line)return;var i=d(r);if(!i)return;if(!n[1]&&"selfClose"!=i){var o=s(r.line,r.ch),a=m(r,n[2]);return a&&0<c(a.from,o)?{from:o,to:a.from}:null}}}),e.findMatchingTag=function(e,t,r){var n=new u(e,t.line,t.ch,r);if(-1!=n.text.indexOf(">")||-1!=n.text.indexOf("<")){var i=d(n),o=i&&s(n.line,n.ch),a=i&&f(n);if(i&&a&&!(0<c(n,t))){var l={from:s(n.line,n.ch),to:o,tag:a[2]};return"selfClose"==i?{open:l,close:null,at:"open"}:a[1]?{open:g(n,a[2]),close:l,at:"close"}:{open:l,close:m(n=new u(e,o.line,o.ch,r),a[2]),at:"open"}}}},e.findEnclosingTag=function(e,t,r,n){for(var i=new u(e,t.line,t.ch,r);;){var o=g(i,n);if(!o)break;var a=m(new u(e,t.line,t.ch,r),o.tag);if(a)return{open:o,close:a}}},e.scanForClosingTag=function(e,t,r,n){return m(new u(e,t.line,t.ch,n?{from:0,to:n}:null),r)}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(A){"use strict";function O(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.info=n,this.align=i,this.prev=o}function D(e,t,r,n){var i=e.indented;return e.context&&"statement"==e.context.type&&"statement"!=r&&(i=e.context.indented),e.context=new O(i,t,r,n,null,e.context)}function z(e){var t=e.context.type;return")"!=t&&"]"!=t&&"}"!=t||(e.indented=e.context.indented),e.context=e.context.prev}function F(e,t,r){return"variable"==t.prevToken||"type"==t.prevToken||(!!/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(e.string.slice(0,r))||(!(!t.typeAtEndOfLine||e.column()!=e.indentation())||void 0))}function I(e){for(;;){if(!e||"top"==e.type)return!0;if("}"==e.type&&"namespace"!=e.prev.info)return!1;e=e.prev}}function e(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function P(e,t){return"function"==typeof e?e(t):e.propertyIsEnumerable(t)}A.defineMode("clike",function(e,l){var a,s,c=e.indentUnit,u=l.statementIndentUnit||c,d=l.dontAlignCalls,f=l.keywords||{},h=l.types||{},p=l.builtin||{},m=l.blockKeywords||{},g=l.defKeywords||{},v=l.atoms||{},y=l.hooks||{},b=l.multiLineStrings,o=!1!==l.indentStatements,t=!1!==l.indentSwitch,w=l.namespaceSeparator,x=l.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,k=l.numberStart||/[\d\.]/,_=l.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,C=l.isOperatorChar||/[+\-*&%=<>!?|\/]/,S=l.isIdentifierChar||/[\w\$_\xa1-\uffff]/,T=l.isReservedIdentifier||!1;function L(e,t){var o,r=e.next();if(y[r]){var n=y[r](e,t);if(!1!==n)return n}if('"'==r||"'"==r)return t.tokenize=(o=r,function(e,t){for(var r,n=!1,i=!1;null!=(r=e.next());){if(r==o&&!n){i=!0;break}n=!n&&"\\"==r}return(i||!n&&!b)&&(t.tokenize=null),"string"}),t.tokenize(e,t);if(x.test(r))return a=r,null;if(k.test(r)){if(e.backUp(1),e.match(_))return"number";e.next()}if("/"==r){if(e.eat("*"))return(t.tokenize=M)(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(C.test(r)){for(;!e.match(/^\/[\/*]/,!1)&&e.eat(C););return"operator"}if(e.eatWhile(S),w)for(;e.match(w);)e.eatWhile(S);var i=e.current();return P(f,i)?(P(m,i)&&(a="newstatement"),P(g,i)&&(s=!0),"keyword"):P(h,i)?"type":P(p,i)||T&&T(i)?(P(m,i)&&(a="newstatement"),"builtin"):P(v,i)?"atom":"variable"}function M(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=null;break}n="*"==r}return"comment"}function N(e,t){l.typeFirstDefinitions&&e.eol()&&I(t.context)&&(t.typeAtEndOfLine=F(e,t,e.pos))}return{startState:function(e){return{tokenize:null,context:new O((e||0)-c,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(e,t){var r=t.context;if(e.sol()&&(null==r.align&&(r.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return N(e,t),null;a=s=null;var n=(t.tokenize||L)(e,t);if("comment"==n||"meta"==n)return n;if(null==r.align&&(r.align=!0),";"==a||":"==a||","==a&&e.match(/^\s*(?:\/\/.*)?$/,!1))for(;"statement"==t.context.type;)z(t);else if("{"==a)D(t,e.column(),"}");else if("["==a)D(t,e.column(),"]");else if("("==a)D(t,e.column(),")");else if("}"==a){for(;"statement"==r.type;)r=z(t);for("}"==r.type&&(r=z(t));"statement"==r.type;)r=z(t)}else a==r.type?z(t):o&&(("}"==r.type||"top"==r.type)&&";"!=a||"statement"==r.type&&"newstatement"==a)&&D(t,e.column(),"statement",e.current());if("variable"==n&&("def"==t.prevToken||l.typeFirstDefinitions&&F(e,t,e.start)&&I(t.context)&&e.match(/^\s*\(/,!1))&&(n="def"),y.token){var i=y.token(e,t,n);void 0!==i&&(n=i)}return"def"==n&&!1===l.styleDefs&&(n="variable"),t.startOfLine=!1,t.prevToken=s?"def":n||a,N(e,t),n},indent:function(e,t){if(e.tokenize!=L&&null!=e.tokenize||e.typeAtEndOfLine)return A.Pass;var r=e.context,n=t&&t.charAt(0),i=n==r.type;if("statement"==r.type&&"}"==n&&(r=r.prev),l.dontIndentStatements)for(;"statement"==r.type&&l.dontIndentStatements.test(r.info);)r=r.prev;if(y.indent){var o=y.indent(e,r,t,c);if("number"==typeof o)return o}var a=r.prev&&"switch"==r.prev.info;if(l.allmanIndentation&&/[{(]/.test(n)){for(;"top"!=r.type&&"}"!=r.type;)r=r.prev;return r.indented}return"statement"==r.type?r.indented+("{"==n?0:u):!r.align||d&&")"==r.type?")"!=r.type||i?r.indented+(i?0:c)+(i||!a||/^(?:case|default)\b/.test(t)?0:c):r.indented+u:r.column+(i?0:1)},electricInput:t?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}});var t="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",r=e("int long char short double float unsigned signed void bool"),n=e("SEL instancetype id Class Protocol BOOL");function i(e){return P(r,e)||/.+_t/.test(e)}var o="case do else for if switch while struct enum union",a="struct enum union";function l(e,t){if(!t.startOfLine)return!1;for(var r,n=null;r=e.peek();){if("\\"==r&&e.match(/^.$/)){n=l;break}if("/"==r&&e.match(/^\/[\/\*]/,!1))break;e.next()}return t.tokenize=n,"meta"}function s(e,t){return"type"==t.prevToken&&"type"}function c(e){return!(!e||e.length<2)&&("_"==e[0]&&("_"==e[1]||e[1]!==e[1].toLowerCase()))}function u(e){return e.eatWhile(/[\w\.']/),"number"}function d(e,t){if(e.backUp(1),e.match(/(R|u8R|uR|UR|LR)/)){var r=e.match(/"([^\s\\()]{0,16})\(/);return!!r&&(t.cpp11RawStringDelim=r[1],(t.tokenize=h)(e,t))}return e.match(/(u8|u|U|L)/)?!!e.match(/["']/,!1)&&"string":(e.next(),!1)}function f(e,t){for(var r;null!=(r=e.next());)if('"'==r&&!e.eat('"')){t.tokenize=null;break}return"string"}function h(e,t){var r=t.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&");return e.match(new RegExp(".*?\\)"+r+'"'))?t.tokenize=null:e.skipToEnd(),"string"}function p(e,t){"string"==typeof e&&(e=[e]);var r=[];function n(e){if(e)for(var t in e)e.hasOwnProperty(t)&&r.push(t)}n(t.keywords),n(t.types),n(t.builtin),n(t.atoms),r.length&&(t.helperType=e[0],A.registerHelper("hintWords",e[0],r));for(var i=0;i<e.length;++i)A.defineMIME(e[i],t)}function m(e,t){for(var r=!1;!e.eol();){if(!r&&e.match('"""')){t.tokenize=null;break}r="\\"==e.next()&&!r}return"string"}p(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:e(t),types:i,blockKeywords:e(o),defKeywords:e(a),typeFirstDefinitions:!0,atoms:e("NULL true false"),isReservedIdentifier:c,hooks:{"#":l,"*":s},modeProps:{fold:["brace","include"]}}),p(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:e(t+"alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq"),types:i,blockKeywords:e(o+" class try catch"),defKeywords:e(a+" class namespace"),typeFirstDefinitions:!0,atoms:e("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:c,hooks:{"#":l,"*":s,u:d,U:d,L:d,R:d,0:u,1:u,2:u,3:u,4:u,5:u,6:u,7:u,8:u,9:u,token:function(e,t,r){if("variable"==r&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&(n=e.current(),(i=/(\w+)::~?(\w+)$/.exec(n))&&i[1]==i[2]))return"def";var n,i}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),p("text/x-java",{name:"clike",keywords:e("abstract assert break case catch class const continue default do else enum extends final finally float for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:e("byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:e("catch class do else finally for if switch try while"),defKeywords:e("class interface enum @interface"),typeFirstDefinitions:!0,atoms:e("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(e){return!e.match("interface",!1)&&(e.eatWhile(/[\w\$_]/),"meta")}},modeProps:{fold:["brace","import"]}}),p("text/x-csharp",{name:"clike",keywords:e("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in interface internal is lock namespace new operator out override params private protected public readonly ref return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:e("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:e("catch class do else finally for foreach if struct switch try while"),defKeywords:e("class interface namespace struct var"),typeFirstDefinitions:!0,atoms:e("true false null"),hooks:{"@":function(e,t){return e.eat('"')?(t.tokenize=f)(e,t):(e.eatWhile(/[\w\$_]/),"meta")}}}),p("text/x-scala",{name:"clike",keywords:e("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:e("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:e("catch class enum do else finally for forSome if match switch try while"),defKeywords:e("class enum def object package trait type val var"),atoms:e("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return!!e.match('""')&&(t.tokenize=m,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},"=":function(e,t){var r=t.context;return!("}"!=r.type||!r.align||!e.eat(">"))&&(t.context=new O(r.indented,r.column,r.type,r.info,null,r.prev),"operator")},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=function n(i){return function(e,t){for(var r;r=e.next();){if("*"==r&&e.eat("/")){if(1==i){t.tokenize=null;break}return t.tokenize=n(i-1),t.tokenize(e,t)}if("/"==r&&e.eat("*"))return t.tokenize=n(i+1),t.tokenize(e,t)}return"comment"}}(1),t.tokenize(e,t))}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}}),p("text/x-kotlin",{name:"clike",keywords:e("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam"),types:e("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:e("catch class do else finally for if where try while enum"),defKeywords:e("class val var object interface fun"),atoms:e("true false null this"),hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},"*":function(e,t){return"."==t.prevToken?"variable":"operator"},'"':function(e,t){var o;return t.tokenize=(o=e.match('""'),function(e,t){for(var r,n=!1,i=!1;!e.eol();){if(!o&&!n&&e.match('"')){i=!0;break}if(o&&e.match('"""')){i=!0;break}r=e.next(),!n&&"$"==r&&e.match("{")&&e.skipTo("}"),n=!n&&"\\"==r&&!o}return!i&&o||(t.tokenize=null),"string"}),t.tokenize(e,t)},indent:function(e,t,r,n){var i=r&&r.charAt(0);return"}"!=e.prevToken&&")"!=e.prevToken||""!=r?"operator"==e.prevToken&&"}"!=r||"variable"==e.prevToken&&"."==i||("}"==e.prevToken||")"==e.prevToken)&&"."==i?2*n+t.indented:t.align&&"}"==t.type?t.indented+(e.context.type==(r||"").charAt(0)?0:n):void 0:e.indented}},modeProps:{closeBrackets:{triples:'"'}}}),p(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:e("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:e("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:e("for while do if else struct"),builtin:e("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:e("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TexureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":l},modeProps:{fold:["brace","include"]}}),p("text/x-nesc",{name:"clike",keywords:e(t+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:i,blockKeywords:e(o),atoms:e("null true false"),hooks:{"#":l},modeProps:{fold:["brace","include"]}}),p("text/x-objectivec",{name:"clike",keywords:e(t+" bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available"),types:function(e){return i(e)||P(n,e)},builtin:e("FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION NS_RETURNS_RETAINED NS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT"),blockKeywords:e(o+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:e(a+" @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:e("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:c,hooks:{"#":l,"*":s},modeProps:{fold:["brace","include"]}}),p("text/x-squirrel",{name:"clike",keywords:e("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:i,blockKeywords:e("case catch class else for foreach if switch try while"),defKeywords:e("function local class"),typeFirstDefinitions:!0,atoms:e("true false null"),hooks:{"#":l},modeProps:{fold:["brace","include"]}});var g=null;p("text/x-ceylon",{name:"clike",keywords:e("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(e){var t=e.charAt(0);return t===t.toUpperCase()&&t!==t.toLowerCase()},blockKeywords:e("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:e("class dynamic function interface module object package value"),builtin:e("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:e("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return t.tokenize=function o(a){return function(e,t){for(var r,n=!1,i=!1;!e.eol();){if(!n&&e.match('"')&&("single"==a||e.match('""'))){i=!0;break}if(!n&&e.match("``")){g=o(a),i=!0;break}r=e.next(),n="single"==a&&!n&&"\\"==r}return i&&(t.tokenize=null),"string"}}(e.match('""')?"triple":"single"),t.tokenize(e,t)},"`":function(e,t){return!(!g||!e.match("`"))&&(t.tokenize=g,g=null,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(e,t,r){if(("variable"==r||"type"==r)&&"."==t.prevToken)return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(A){"use strict";function e(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}A.defineMode("css",function(e,t){var r=t.inline;t.propertyKeywords||(t=A.resolveMode("text/css"));var n,i,o=e.indentUnit,a=t.tokenHooks,l=t.documentTypes||{},s=t.mediaTypes||{},c=t.mediaFeatures||{},u=t.mediaValueKeywords||{},d=t.propertyKeywords||{},f=t.nonStandardPropertyKeywords||{},h=t.fontProperties||{},p=t.counterDescriptors||{},m=t.colorKeywords||{},g=t.valueKeywords||{},v=t.allowNested,y=t.lineComment,b=!0===t.supportsAtComponent;function w(e,t){return n=t,e}function x(i){return function(e,t){for(var r,n=!1;null!=(r=e.next());){if(r==i&&!n){")"==i&&e.backUp(1);break}n=!n&&"\\"==r}return(r==i||!n&&")"!=i)&&(t.tokenize=null),w("string","string")}}function k(e,t){return e.next(),e.match(/\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=x(")"),w(null,"(")}function _(e,t,r){this.type=e,this.indent=t,this.prev=r}function C(e,t,r,n){return e.context=new _(r,t.indentation()+(!1===n?0:o),e.context),r}function S(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function T(e,t,r){return N[r.context.type](e,t,r)}function L(e,t,r,n){for(var i=n||1;0<i;i--)r.context=r.context.prev;return T(e,t,r)}function M(e){var t=e.current().toLowerCase();i=g.hasOwnProperty(t)?"atom":m.hasOwnProperty(t)?"keyword":"variable"}var N={top:function(e,t,r){if("{"==e)return C(r,t,"block");if("}"==e&&r.context.prev)return S(r);if(b&&/@component/i.test(e))return C(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return C(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return C(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return C(r,t,"at");if("hash"==e)i="builtin";else if("word"==e)i="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return C(r,t,"interpolation");if(":"==e)return"pseudo";if(v&&"("==e)return C(r,t,"parens")}return r.context.type},block:function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return d.hasOwnProperty(n)?(i="property","maybeprop"):f.hasOwnProperty(n)?(i="string-2","maybeprop"):v?(i=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(i+=" error","maybeprop")}return"meta"==e?"block":v||"hash"!=e&&"qualifier"!=e?N.top(e,t,r):(i="error","block")},maybeprop:function(e,t,r){return":"==e?C(r,t,"prop"):T(e,t,r)},prop:function(e,t,r){if(";"==e)return S(r);if("{"==e&&v)return C(r,t,"propBlock");if("}"==e||"{"==e)return L(e,t,r);if("("==e)return C(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(t.current())){if("word"==e)M(t);else if("interpolation"==e)return C(r,t,"interpolation")}else i+=" error";return"prop"},propBlock:function(e,t,r){return"}"==e?S(r):"word"==e?(i="property","maybeprop"):r.context.type},parens:function(e,t,r){return"{"==e||"}"==e?L(e,t,r):")"==e?S(r):"("==e?C(r,t,"parens"):"interpolation"==e?C(r,t,"interpolation"):("word"==e&&M(t),"parens")},pseudo:function(e,t,r){return"meta"==e?"pseudo":"word"==e?(i="variable-3",r.context.type):T(e,t,r)},documentTypes:function(e,t,r){return"word"==e&&l.hasOwnProperty(t.current())?(i="tag",r.context.type):N.atBlock(e,t,r)},atBlock:function(e,t,r){if("("==e)return C(r,t,"atBlock_parens");if("}"==e||";"==e)return L(e,t,r);if("{"==e)return S(r)&&C(r,t,v?"block":"top");if("interpolation"==e)return C(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();i="only"==n||"not"==n||"and"==n||"or"==n?"keyword":s.hasOwnProperty(n)?"attribute":c.hasOwnProperty(n)?"property":u.hasOwnProperty(n)?"keyword":d.hasOwnProperty(n)?"property":f.hasOwnProperty(n)?"string-2":g.hasOwnProperty(n)?"atom":m.hasOwnProperty(n)?"keyword":"error"}return r.context.type},atComponentBlock:function(e,t,r){return"}"==e?L(e,t,r):"{"==e?S(r)&&C(r,t,v?"block":"top",!1):("word"==e&&(i="error"),r.context.type)},atBlock_parens:function(e,t,r){return")"==e?S(r):"{"==e||"}"==e?L(e,t,r,2):N.atBlock(e,t,r)},restricted_atBlock_before:function(e,t,r){return"{"==e?C(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(i="variable","restricted_atBlock_before"):T(e,t,r)},restricted_atBlock:function(e,t,r){return"}"==e?(r.stateArg=null,S(r)):"word"==e?(i="@font-face"==r.stateArg&&!h.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!p.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,r){return"word"==e?(i="variable","keyframes"):"{"==e?C(r,t,"top"):T(e,t,r)},at:function(e,t,r){return";"==e?S(r):"{"==e||"}"==e?L(e,t,r):("word"==e?i="tag":"hash"==e&&(i="builtin"),"at")},interpolation:function(e,t,r){return"}"==e?S(r):"{"==e||";"==e?L(e,t,r):("word"==e?i="variable":"variable"!=e&&"("!=e&&")"!=e&&(i="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:r?"block":"top",stateArg:null,context:new _(r?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||function(e,t){var r=e.next();if(a[r]){var n=a[r](e,t);if(!1!==n)return n}return"@"==r?(e.eatWhile(/[\w\\\-]/),w("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?w(null,"compare"):'"'==r||"'"==r?(t.tokenize=x(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),w("atom","hash")):"!"==r?(e.match(/^\s*\w*/),w("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),w("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?w(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?w("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?w(null,r):("u"==r||"U"==r)&&e.match(/rl(-prefix)?\(/i)||("d"==r||"D"==r)&&e.match("omain(",!0,!0)||("r"==r||"R"==r)&&e.match("egexp(",!0,!0)?(e.backUp(1),t.tokenize=k,w("property","word")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),w("property","word")):w(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),w("number","unit")):e.match(/^-[\w\\\-]+/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?w("variable-2","variable-definition"):w("variable-2","variable")):e.match(/^\w+-/)?w("meta","meta"):void 0})(e,t);return r&&"object"==typeof r&&(n=r[1],r=r[0]),i=r,"comment"!=n&&(t.state=N[t.state](n,e,t)),i},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),i=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(i=Math.max(0,r.indent-o)):i=(r=r.prev).indent),i},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:y,fold:"brace"}});var t=["domain","regexp","url","url-prefix"],r=e(t),n=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],i=e(n),o=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover"],a=e(o),l=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive"],s=e(l),c=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","baseline-shift","binding","bleed","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-feature-settings","font-family","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","max-height","max-width","min-height","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotation","rotation-point","ruby-align","ruby-overhang","ruby-position","ruby-span","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-outline","text-overflow","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],u=e(c),d=["scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-3d-light-color","scrollbar-track-color","shape-inside","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","zoom"],f=e(d),h=e(["font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),p=e(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),m=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],g=e(m),v=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","match","matrix","matrix3d","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","sub","subpixel-antialiased","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],y=e(v),b=t.concat(n).concat(o).concat(l).concat(c).concat(d).concat(m).concat(v);function w(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}A.registerHelper("hintWords","css",b),A.defineMIME("text/css",{documentTypes:r,mediaTypes:i,mediaFeatures:a,mediaValueKeywords:s,propertyKeywords:u,nonStandardPropertyKeywords:f,fontProperties:h,counterDescriptors:p,colorKeywords:g,valueKeywords:y,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=w)(e,t)}},name:"css"}),A.defineMIME("text/x-scss",{mediaTypes:i,mediaFeatures:a,mediaValueKeywords:s,propertyKeywords:u,nonStandardPropertyKeywords:f,colorKeywords:g,valueKeywords:y,fontProperties:h,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=w)(e,t):["operator","operator"]},":":function(e){return!!e.match(/\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),A.defineMIME("text/x-less",{mediaTypes:i,mediaFeatures:a,mediaValueKeywords:s,propertyKeywords:u,nonStandardPropertyKeywords:f,colorKeywords:g,valueKeywords:y,fontProperties:h,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=w)(e,t):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),A.defineMIME("text/x-gss",{documentTypes:r,mediaTypes:i,mediaFeatures:a,propertyKeywords:u,nonStandardPropertyKeywords:f,fontProperties:h,counterDescriptors:p,colorKeywords:g,valueKeywords:y,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=w)(e,t)}},name:"css",helperType:"gss"})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../xml/xml"),require("../javascript/javascript"),require("../css/css")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","../xml/xml","../javascript/javascript","../css/css"],e):e(CodeMirror)}(function(p){"use strict";var i={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};var o={};function m(e,t){var r,n=e.match(o[r=t]||(o[r]=new RegExp("\\s+"+r+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*")));return n?/^\s*(.*?)\s*$/.exec(n[2])[1]:""}function g(e,t){return new RegExp((t?"^":"")+"</s*"+e+"s*>","i")}function a(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),i=e[r],o=i.length-1;0<=o;o--)n.unshift(i[o])}p.defineMode("htmlmixed",function(u,e){var d=p.getMode(u,{name:"xml",htmlMode:!0,multilineTagIndentFactor:e.multilineTagIndentFactor,multilineTagIndentPastTag:e.multilineTagIndentPastTag}),f={},t=e&&e.tags,r=e&&e.scriptTypes;if(a(i,f),t&&a(t,f),r)for(var n=r.length-1;0<=n;n--)f.script.unshift(["type",r[n].matches,r[n].mode]);function h(e,t){var r,n=d.token(e,t.htmlState),i=/\btag\b/.test(n);if(i&&!/[<>\s\/]/.test(e.current())&&(r=t.htmlState.tagName&&t.htmlState.tagName.toLowerCase())&&f.hasOwnProperty(r))t.inTag=r+" ";else if(t.inTag&&i&&/>$/.test(e.current())){var o=/^([\S]+) (.*)/.exec(t.inTag);t.inTag=null;var a=">"==e.current()&&function(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(!n[0]||n[1].test(m(t,n[0])))return n[2]}}(f[o[1]],o[2]),l=p.getMode(u,a),s=g(o[1],!0),c=g(o[1],!1);t.token=function(e,t){return e.match(s,!1)?(t.token=h,t.localState=t.localMode=null,null):(r=e,n=c,i=t.localMode.token(e,t.localState),o=r.current(),-1<(a=o.search(n))?r.backUp(o.length-a):o.match(/<\/?$/)&&(r.backUp(o.length),r.match(n,!1)||r.match(o)),i);var r,n,i,o,a},t.localMode=l,t.localState=p.startState(l,d.indent(t.htmlState,""))}else t.inTag&&(t.inTag+=e.current(),e.eol()&&(t.inTag+=" "));return n}return{startState:function(){return{token:h,inTag:null,localMode:null,localState:null,htmlState:p.startState(d)}},copyState:function(e){var t;return e.localState&&(t=p.copyState(e.localMode,e.localState)),{token:e.token,inTag:e.inTag,localMode:e.localMode,localState:t,htmlState:p.copyState(d,e.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(e,t,r){return!e.localMode||/^\s*<\//.test(t)?d.indent(e.htmlState,t):e.localMode.indent?e.localMode.indent(e.localState,t,r):p.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||d}}}},"xml","javascript","css"),p.defineMIME("text/html","htmlmixed")}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function($e){"use strict";$e.defineMode("javascript",function(e,d){var n,i,f=e.indentUnit,h=d.statementIndent,a=d.jsonld,l=d.json||a,c=d.typescript,u=d.wordCharacters||/[\w$\xa1-\uffff]/,s=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),i=e("keyword d"),o=e("operator"),a={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:i,break:i,continue:i,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:a,false:a,null:a,undefined:a,NaN:a,Infinity:a,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),p=/[+\-*&%=<>!?|~^@]/,m=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function g(e,t,r){return n=e,i=r,t}function v(e,t){var i,r=e.next();if('"'==r||"'"==r)return t.tokenize=(i=r,function(e,t){var r,n=!1;if(a&&"@"==e.peek()&&e.match(m))return t.tokenize=v,g("jsonld-keyword","meta");for(;null!=(r=e.next())&&(r!=i||n);)n=!n&&"\\"==r;return n||(t.tokenize=v),g("string","string")}),t.tokenize(e,t);if("."==r&&e.match(/^\d+(?:[eE][+\-]?\d+)?/))return g("number","number");if("."==r&&e.match(".."))return g("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return g(r);if("="==r&&e.eat(">"))return g("=>","operator");if("0"==r&&e.match(/^(?:x[\da-f]+|o[0-7]+|b[01]+)n?/i))return g("number","number");if(/\d/.test(r))return e.match(/^\d*(?:n|(?:\.\d*)?(?:[eE][+\-]?\d+)?)?/),g("number","number");if("/"==r)return e.eat("*")?(t.tokenize=y)(e,t):e.eat("/")?(e.skipToEnd(),g("comment","comment")):Ve(e,t,1)?(function(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),g("regexp","string-2")):(e.eat("="),g("operator","operator",e.current()));if("`"==r)return(t.tokenize=b)(e,t);if("#"==r)return e.skipToEnd(),g("error","error");if(p.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),g("operator","operator",e.current());if(u.test(r)){e.eatWhile(u);var n=e.current();if("."!=t.lastType){if(s.propertyIsEnumerable(n)){var o=s[n];return g(o.type,o.style,n)}if("async"==n&&e.match(/^(\s|\/\*.*?\*\/)*[\[\(\w]/,!1))return g("async","keyword",n)}return g("variable","variable",n)}}function y(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=v;break}n="*"==r}return g("comment","comment")}function b(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=v;break}n=!n&&"\\"==r}return g("quasi","string-2",e.current())}var w="([{}])";function o(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(c){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var i=0,o=!1,a=r-1;0<=a;--a){var l=e.string.charAt(a),s=w.indexOf(l);if(0<=s&&s<3){if(!i){++a;break}if(0==--i){"("==l&&(o=!0);break}}else if(3<=s&&s<6)++i;else if(u.test(l))o=!0;else{if(/["'\/]/.test(l))return;if(o&&!i){++a;break}}}o&&!i&&(t.fatArrowAt=a)}}var x={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,"jsonld-keyword":!0};function k(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.prev=i,this.info=o,null!=n&&(this.align=n)}function _(e,t){for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}var C={state:null,column:null,marked:null,cc:null};function S(){for(var e=arguments.length-1;0<=e;e--)C.cc.push(arguments[e])}function T(){return S.apply(null,arguments),!0}function L(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function r(e){var t=C.state;if(C.marked="def",t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var r=function e(t,r){{if(r){if(r.block){var n=e(t,r.prev);return n?n==r.prev?r:new N(n,r.vars,!0):null}return L(t,r.vars)?r:new N(r.prev,new A(t,r.vars),!1)}return null}}(e,t.context);if(null!=r)return void(t.context=r)}else if(!L(e,t.localVars))return void(t.localVars=new A(e,t.localVars));d.globalVars&&!L(e,t.globalVars)&&(t.globalVars=new A(e,t.globalVars))}function M(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function N(e,t,r){this.prev=e,this.vars=t,this.block=r}function A(e,t){this.name=e,this.next=t}var t=new A("this",new A("arguments",null));function O(){C.state.context=new N(C.state.context,C.state.localVars,!1),C.state.localVars=t}function D(){C.state.context=new N(C.state.context,C.state.localVars,!0),C.state.localVars=null}function z(){C.state.localVars=C.state.context.vars,C.state.context=C.state.context.prev}function F(n,i){var e=function(){var e=C.state,t=e.indented;if("stat"==e.lexical.type)t=e.lexical.indented;else for(var r=e.lexical;r&&")"==r.type&&r.align;r=r.prev)t=r.indented;e.lexical=new k(t,C.stream.column(),n,null,e.lexical,i)};return e.lex=!0,e}function I(){var e=C.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function P(r){return function e(t){return t==r?T():";"==r||"}"==t||")"==t||"]"==t?S():T(e)}}function E(e,t){return"var"==e?T(F("vardef",t),ve,P(";"),I):"keyword a"==e?T(F("form"),R,E,I):"keyword b"==e?T(F("form"),E,I):"keyword d"==e?C.stream.match(/^\s*$/,!1)?T():T(F("stat"),j,P(";"),I):"debugger"==e?T(P(";")):"{"==e?T(F("}"),D,oe,I,z):";"==e?T():"if"==e?("else"==C.state.lexical.info&&C.state.cc[C.state.cc.length-1]==I&&C.state.cc.pop()(),T(F("form"),R,E,I,_e)):"function"==e?T(Ne):"for"==e?T(F("form"),Ce,E,I):"class"==e||c&&"interface"==t?(C.marked="keyword",T(F("form"),De,I)):"variable"==e?c&&"declare"==t?(C.marked="keyword",T(E)):c&&("module"==t||"enum"==t||"type"==t)&&C.stream.match(/^\s*\w/,!1)?(C.marked="keyword","enum"==t?T(Ue):"type"==t?T(ce,P("operator"),ce,P(";")):T(F("form"),ye,P("{"),F("}"),oe,I,I)):c&&"namespace"==t?(C.marked="keyword",T(F("form"),W,oe,I)):c&&"abstract"==t?(C.marked="keyword",T(E)):T(F("stat"),Q):"switch"==e?T(F("form"),R,P("{"),F("}","switch"),D,oe,I,I,z):"case"==e?T(W,P(":")):"default"==e?T(P(":")):"catch"==e?T(F("form"),O,q,E,I,z):"export"==e?T(F("stat"),Pe,I):"import"==e?T(F("stat"),qe,I):"async"==e?T(E):"@"==t?T(W,E):S(F("stat"),W,P(";"),I)}function q(e){if("("==e)return T(Ae,P(")"))}function W(e,t){return B(e,t,!1)}function H(e,t){return B(e,t,!0)}function R(e){return"("!=e?S():T(F(")"),W,P(")"),I)}function B(e,t,r){if(C.state.fatArrowAt==C.stream.start){var n=r?X:G;if("("==e)return T(O,F(")"),ne(Ae,")"),I,P("=>"),n,z);if("variable"==e)return S(O,ye,P("=>"),n,z)}var i,o=r?K:U;return x.hasOwnProperty(e)?T(o):"function"==e?T(Ne,o):"class"==e||c&&"interface"==t?(C.marked="keyword",T(F("form"),Oe,I)):"keyword c"==e||"async"==e?T(r?H:W):"("==e?T(F(")"),j,P(")"),I,o):"operator"==e||"spread"==e?T(r?H:W):"["==e?T(F("]"),je,I,o):"{"==e?ie(ee,"}",null,o):"quasi"==e?S(V,o):"new"==e?T((i=r,function(e){return"."==e?T(i?Z:Y):"variable"==e&&c?T(pe,i?K:U):S(i?H:W)})):"import"==e?T(W):T()}function j(e){return e.match(/[;\}\)\],]/)?S():S(W)}function U(e,t){return","==e?T(W):K(e,t,!1)}function K(e,t,r){var n=0==r?U:K,i=0==r?W:H;return"=>"==e?T(O,r?X:G,z):"operator"==e?/\+\+|--/.test(t)||c&&"!"==t?T(n):c&&"<"==t&&C.stream.match(/^([^>]|<.*?>)*>\s*\(/,!1)?T(F(">"),ne(ce,">"),I,n):"?"==t?T(W,P(":"),i):T(i):"quasi"==e?S(V,n):";"!=e?"("==e?ie(H,")","call",n):"."==e?T(J,n):"["==e?T(F("]"),j,P("]"),I,n):c&&"as"==t?(C.marked="keyword",T(ce,n)):"regexp"==e?(C.state.lastType=C.marked="operator",C.stream.backUp(C.stream.pos-C.stream.start-1),T(i)):void 0:void 0}function V(e,t){return"quasi"!=e?S():"${"!=t.slice(t.length-2)?T(V):T(W,$)}function $(e){if("}"==e)return C.marked="string-2",C.state.tokenize=b,T(V)}function G(e){return o(C.stream,C.state),S("{"==e?E:W)}function X(e){return o(C.stream,C.state),S("{"==e?E:H)}function Y(e,t){if("target"==t)return C.marked="keyword",T(U)}function Z(e,t){if("target"==t)return C.marked="keyword",T(K)}function Q(e){return":"==e?T(I,E):S(U,P(";"),I)}function J(e){if("variable"==e)return C.marked="property",T()}function ee(e,t){if("async"==e)return C.marked="property",T(ee);if("variable"==e||"keyword"==C.style){return C.marked="property","get"==t||"set"==t?T(te):(c&&C.state.fatArrowAt==C.stream.start&&(r=C.stream.match(/^\s*:\s*/,!1))&&(C.state.fatArrowAt=C.stream.pos+r[0].length),T(re));var r}else{if("number"==e||"string"==e)return C.marked=a?"property":C.style+" property",T(re);if("jsonld-keyword"==e)return T(re);if(c&&M(t))return C.marked="keyword",T(ee);if("["==e)return T(W,ae,P("]"),re);if("spread"==e)return T(H,re);if("*"==t)return C.marked="keyword",T(ee);if(":"==e)return S(re)}}function te(e){return"variable"!=e?S(re):(C.marked="property",T(Ne))}function re(e){return":"==e?T(H):"("==e?S(Ne):void 0}function ne(n,i,o){function a(e,t){if(o?-1<o.indexOf(e):","==e){var r=C.state.lexical;return"call"==r.info&&(r.pos=(r.pos||0)+1),T(function(e,t){return e==i||t==i?S():S(n)},a)}return e==i||t==i?T():T(P(i))}return function(e,t){return e==i||t==i?T():S(n,a)}}function ie(e,t,r){for(var n=3;n<arguments.length;n++)C.cc.push(arguments[n]);return T(F(t,r),ne(e,t),I)}function oe(e){return"}"==e?T():S(E,oe)}function ae(e,t){if(c){if(":"==e)return T(ce);if("?"==t)return T(ae)}}function le(e){if(c&&":"==e)return C.stream.match(/^\s*\w+\s+is\b/,!1)?T(W,se,ce):T(ce)}function se(e,t){if("is"==t)return C.marked="keyword",T()}function ce(e,t){return"keyof"==t||"typeof"==t?(C.marked="keyword",T("keyof"==t?ce:H)):"variable"==e||"void"==t?(C.marked="type",T(he)):"string"==e||"number"==e||"atom"==e?T(he):"["==e?T(F("]"),ne(ce,"]",","),I,he):"{"==e?T(F("}"),ne(de,"}",",;"),I,he):"("==e?T(ne(fe,")"),ue):"<"==e?T(ne(ce,">"),ce):void 0}function ue(e){if("=>"==e)return T(ce)}function de(e,t){return"variable"==e||"keyword"==C.style?(C.marked="property",T(de)):"?"==t?T(de):":"==e?T(ce):"["==e?T(W,ae,P("]"),de):void 0}function fe(e,t){return"variable"==e&&C.stream.match(/^\s*[?:]/,!1)||"?"==t?T(fe):":"==e?T(ce):S(ce)}function he(e,t){return"<"==t?T(F(">"),ne(ce,">"),I,he):"|"==t||"."==e||"&"==t?T(ce):"["==e?T(P("]"),he):"extends"==t||"implements"==t?(C.marked="keyword",T(ce)):void 0}function pe(e,t){if("<"==t)return T(F(">"),ne(ce,">"),I,he)}function me(){return S(ce,ge)}function ge(e,t){if("="==t)return T(ce)}function ve(e,t){return"enum"==t?(C.marked="keyword",T(Ue)):S(ye,ae,xe,ke)}function ye(e,t){return c&&M(t)?(C.marked="keyword",T(ye)):"variable"==e?(r(t),T()):"spread"==e?T(ye):"["==e?ie(we,"]"):"{"==e?ie(be,"}"):void 0}function be(e,t){return"variable"!=e||C.stream.match(/^\s*:/,!1)?("variable"==e&&(C.marked="property"),"spread"==e?T(ye):"}"==e?S():"["==e?T(W,P("]"),P(":"),be):T(P(":"),ye,xe)):(r(t),T(xe))}function we(){return S(ye,xe)}function xe(e,t){if("="==t)return T(H)}function ke(e){if(","==e)return T(ve)}function _e(e,t){if("keyword b"==e&&"else"==t)return T(F("form","else"),E,I)}function Ce(e,t){return"await"==t?T(Ce):"("==e?T(F(")"),Se,P(")"),I):void 0}function Se(e){return"var"==e?T(ve,P(";"),Le):";"==e?T(Le):"variable"==e?T(Te):S(W,P(";"),Le)}function Te(e,t){return"in"==t||"of"==t?(C.marked="keyword",T(W)):T(U,Le)}function Le(e,t){return";"==e?T(Me):"in"==t||"of"==t?(C.marked="keyword",T(W)):S(W,P(";"),Me)}function Me(e){")"!=e&&T(W)}function Ne(e,t){return"*"==t?(C.marked="keyword",T(Ne)):"variable"==e?(r(t),T(Ne)):"("==e?T(O,F(")"),ne(Ae,")"),I,le,E,z):c&&"<"==t?T(F(">"),ne(me,">"),I,Ne):void 0}function Ae(e,t){return"@"==t&&T(W,Ae),"spread"==e?T(Ae):c&&M(t)?(C.marked="keyword",T(Ae)):S(ye,ae,xe)}function Oe(e,t){return"variable"==e?De(e,t):ze(e,t)}function De(e,t){if("variable"==e)return r(t),T(ze)}function ze(e,t){return"<"==t?T(F(">"),ne(me,">"),I,ze):"extends"==t||"implements"==t||c&&","==e?("implements"==t&&(C.marked="keyword"),T(c?ce:W,ze)):"{"==e?T(F("}"),Fe,I):void 0}function Fe(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||c&&M(t))&&C.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(C.marked="keyword",T(Fe)):"variable"==e||"keyword"==C.style?(C.marked="property",T(c?Ie:Ne,Fe)):"["==e?T(W,ae,P("]"),c?Ie:Ne,Fe):"*"==t?(C.marked="keyword",T(Fe)):";"==e?T(Fe):"}"==e?T():"@"==t?T(W,Fe):void 0}function Ie(e,t){return"?"==t?T(Ie):":"==e?T(ce,xe):"="==t?T(H):S(Ne)}function Pe(e,t){return"*"==t?(C.marked="keyword",T(Be,P(";"))):"default"==t?(C.marked="keyword",T(W,P(";"))):"{"==e?T(ne(Ee,"}"),Be,P(";")):S(E)}function Ee(e,t){return"as"==t?(C.marked="keyword",T(P("variable"))):"variable"==e?S(H,Ee):void 0}function qe(e){return"string"==e?T():"("==e?S(W):S(We,He,Be)}function We(e,t){return"{"==e?ie(We,"}"):("variable"==e&&r(t),"*"==t&&(C.marked="keyword"),T(Re))}function He(e){if(","==e)return T(We,He)}function Re(e,t){if("as"==t)return C.marked="keyword",T(We)}function Be(e,t){if("from"==t)return C.marked="keyword",T(W)}function je(e){return"]"==e?T():S(ne(H,"]"))}function Ue(){return S(F("form"),ye,P("{"),F("}"),ne(Ke,"}"),I,I)}function Ke(){return S(ye,xe)}function Ve(e,t,r){return t.tokenize==v&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return I.lex=z.lex=!0,{startState:function(e){var t={tokenize:v,lastType:"sof",cc:[],lexical:new k((e||0)-f,0,"block",!1),localVars:d.localVars,context:d.localVars&&new N(null,null,!1),indented:e||0};return d.globalVars&&"object"==typeof d.globalVars&&(t.globalVars=d.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),o(e,t)),t.tokenize!=y&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==n?r:(t.lastType="operator"!=n||"++"!=i&&"--"!=i?n:"incdec",function(e,t,r,n,i){var o=e.cc;for(C.state=e,C.stream=i,C.marked=null,C.cc=o,C.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():l?W:E)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return C.marked?C.marked:"variable"==r&&_(e,n)?"variable-2":t}}(t,r,n,i,e))},indent:function(e,t){if(e.tokenize==y)return $e.Pass;if(e.tokenize!=v)return 0;var r,n=t&&t.charAt(0),i=e.lexical;if(!/^\s*else\b/.test(t))for(var o=e.cc.length-1;0<=o;--o){var a=e.cc[o];if(a==I)i=i.prev;else if(a!=_e)break}for(;("stat"==i.type||"form"==i.type)&&("}"==n||(r=e.cc[e.cc.length-1])&&(r==U||r==K)&&!/^[,\.=+\-*:?[\(]/.test(t));)i=i.prev;h&&")"==i.type&&"stat"==i.prev.type&&(i=i.prev);var l,s,c=i.type,u=n==c;return"vardef"==c?i.indented+("operator"==e.lastType||","==e.lastType?i.info.length+1:0):"form"==c&&"{"==n?i.indented:"form"==c?i.indented+f:"stat"==c?i.indented+(s=t,"operator"==(l=e).lastType||","==l.lastType||p.test(s.charAt(0))||/[,.]/.test(s.charAt(0))?h||f:0):"switch"!=i.info||u||0==d.doubleIndentSwitch?i.align?i.column+(u?0:1):i.indented+(u?0:f):i.indented+(/^(?:case|default)\b/.test(t)?f:2*f)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:l?null:"/*",blockCommentEnd:l?null:"*/",blockCommentContinue:l?null:" * ",lineComment:l?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:l?"json":"javascript",jsonldMode:a,jsonMode:l,expressionAllowed:Ve,skipExpression:function(e){var t=e.cc[e.cc.length-1];t!=W&&t!=H||e.cc.pop()}}}),$e.registerHelper("wordChars","javascript",/[\w$]/),$e.defineMIME("text/javascript","javascript"),$e.defineMIME("text/ecmascript","javascript"),$e.defineMIME("application/javascript","javascript"),$e.defineMIME("application/x-javascript","javascript"),$e.defineMIME("application/ecmascript","javascript"),$e.defineMIME("application/json",{name:"javascript",json:!0}),$e.defineMIME("application/x-json",{name:"javascript",json:!0}),$e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),$e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),$e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(n){var u=/MSIE \d/.test(navigator.userAgent)&&(null==document.documentMode||document.documentMode<8),m=n.Pos,g={"(":")>",")":"(<","[":"]>","]":"[<","{":"}>","}":"{<"};function d(e,t,r){var n=e.getLineHandle(t.line),i=t.ch-1,o=r&&r.afterCursor;null==o&&(o=/(^| )cm-fat-cursor($| )/.test(e.getWrapperElement().className));var a=!o&&0<=i&&g[n.text.charAt(i)]||g[n.text.charAt(++i)];if(!a)return null;var l=">"==a.charAt(1)?1:-1;if(r&&r.strict&&0<l!=(i==t.ch))return null;var s=e.getTokenTypeAt(m(t.line,i+1)),c=f(e,m(t.line,i+(0<l?1:0)),l,s||null,r);return null==c?null:{from:m(t.line,i),to:c&&c.pos,match:c&&c.ch==a.charAt(0),forward:0<l}}function f(e,t,r,n,i){for(var o=i&&i.maxScanLineLength||1e4,a=i&&i.maxScanLines||1e3,l=[],s=i&&i.bracketRegex?i.bracketRegex:/[(){}[\]]/,c=0<r?Math.min(t.line+a,e.lastLine()+1):Math.max(e.firstLine()-1,t.line-a),u=t.line;u!=c;u+=r){var d=e.getLine(u);if(d){var f=0<r?0:d.length-1,h=0<r?d.length:-1;if(!(d.length>o))for(u==t.line&&(f=t.ch-(r<0?1:0));f!=h;f+=r){var p=d.charAt(f);if(s.test(p)&&(void 0===n||e.getTokenTypeAt(m(u,f+1))==n))if(">"==g[p].charAt(1)==0<r)l.push(p);else{if(!l.length)return{pos:m(u,f),ch:p};l.pop()}}}}return u-r!=(0<r?e.lastLine():e.firstLine())&&null}function t(e,t,r){for(var n=e.state.matchBrackets.maxHighlightLineLength||1e3,i=[],o=e.listSelections(),a=0;a<o.length;a++){var l=o[a].empty()&&d(e,o[a].head,r);if(l&&e.getLine(l.from.line).length<=n){var s=l.match?"CodeMirror-matchingbracket":"CodeMirror-nonmatchingbracket";i.push(e.markText(l.from,m(l.from.line,l.from.ch+1),{className:s})),l.to&&e.getLine(l.to.line).length<=n&&i.push(e.markText(l.to,m(l.to.line,l.to.ch+1),{className:s}))}}if(i.length){u&&e.state.focused&&e.focus();var c=function(){e.operation(function(){for(var e=0;e<i.length;e++)i[e].clear()})};if(!t)return c;setTimeout(c,800)}}function i(e){e.operation(function(){e.state.matchBrackets.currentlyHighlighted&&(e.state.matchBrackets.currentlyHighlighted(),e.state.matchBrackets.currentlyHighlighted=null),e.state.matchBrackets.currentlyHighlighted=t(e,!1,e.state.matchBrackets)})}n.defineOption("matchBrackets",!1,function(e,t,r){r&&r!=n.Init&&(e.off("cursorActivity",i),e.state.matchBrackets&&e.state.matchBrackets.currentlyHighlighted&&(e.state.matchBrackets.currentlyHighlighted(),e.state.matchBrackets.currentlyHighlighted=null)),t&&(e.state.matchBrackets="object"==typeof t?t:{},e.on("cursorActivity",i))}),n.defineExtension("matchBrackets",function(){t(this,!0)}),n.defineExtension("findMatchingBracket",function(e,t,r){return(r||"boolean"==typeof t)&&(r?(r.strict=t,t=r):t=t?{strict:!0}:null),d(this,e,t)}),n.defineExtension("scanForBracket",function(e,t,r,n){return f(this,e,t,r,n)})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../htmlmixed/htmlmixed"),require("../clike/clike")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","../htmlmixed/htmlmixed","../clike/clike"],e):e(CodeMirror)}(function(c){"use strict";function e(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function l(i,o,a){return 0==i.length?s(o):function(e,t){for(var r=i[0],n=0;n<r.length;n++)if(e.match(r[n][0]))return t.tokenize=l(i.slice(1),o),r[n][1];return t.tokenize=s(o,a),"string"}}function s(r,n){return function(e,t){return function(e,t,r,n){if(!1!==n&&e.match("${",!1)||e.match("{$",!1))return t.tokenize=null,"string";if(!1!==n&&e.match(/^\$[a-zA-Z_][a-zA-Z0-9_]*/))return e.match("[",!1)&&(t.tokenize=l([[["[",null]],[[/\d[\w\.]*/,"number"],[/\$[a-zA-Z_][a-zA-Z0-9_]*/,"variable-2"],[/[\w\$]+/,"variable"]],[["]",null]]],r,n)),e.match(/\-\>\w/,!1)&&(t.tokenize=l([[["->",null]],[[/[\w]+/,"variable"]]],r,n)),"variable-2";var i=!1;for(;!e.eol()&&(i||!1===n||!e.match("{$",!1)&&!e.match(/^(\$[a-zA-Z_][a-zA-Z0-9_]*|\$\{)/,!1));){if(!i&&e.match(r)){t.tokenize=null,t.tokStack.pop(),t.tokStack.pop();break}i="\\"==e.next()&&!i}return"string"}(e,t,r,n)}}var t="abstract and array as break case catch class clone const continue declare default do else elseif enddeclare endfor endforeach endif endswitch endwhile extends final for foreach function global goto if implements interface instanceof namespace new or private protected public static switch throw trait try use var while xor die echo empty exit eval include include_once isset list require require_once return print unset __halt_compiler self static parent yield insteadof finally",r="true false null TRUE FALSE NULL __CLASS__ __DIR__ __FILE__ __LINE__ __METHOD__ __FUNCTION__ __NAMESPACE__ __TRAIT__",n="func_num_args func_get_arg func_get_args strlen strcmp strncmp strcasecmp strncasecmp each error_reporting define defined trigger_error user_error set_error_handler restore_error_handler get_declared_classes get_loaded_extensions extension_loaded get_extension_funcs debug_backtrace constant bin2hex hex2bin sleep usleep time mktime gmmktime strftime gmstrftime strtotime date gmdate getdate localtime checkdate flush wordwrap htmlspecialchars htmlentities html_entity_decode md5 md5_file crc32 getimagesize image_type_to_mime_type phpinfo phpversion phpcredits strnatcmp strnatcasecmp substr_count strspn strcspn strtok strtoupper strtolower strpos strrpos strrev hebrev hebrevc nl2br basename dirname pathinfo stripslashes stripcslashes strstr stristr strrchr str_shuffle str_word_count strcoll substr substr_replace quotemeta ucfirst ucwords strtr addslashes addcslashes rtrim str_replace str_repeat count_chars chunk_split trim ltrim strip_tags similar_text explode implode setlocale localeconv parse_str str_pad chop strchr sprintf printf vprintf vsprintf sscanf fscanf parse_url urlencode urldecode rawurlencode rawurldecode readlink linkinfo link unlink exec system escapeshellcmd escapeshellarg passthru shell_exec proc_open proc_close rand srand getrandmax mt_rand mt_srand mt_getrandmax base64_decode base64_encode abs ceil floor round is_finite is_nan is_infinite bindec hexdec octdec decbin decoct dechex base_convert number_format fmod ip2long long2ip getenv putenv getopt microtime gettimeofday getrusage uniqid quoted_printable_decode set_time_limit get_cfg_var magic_quotes_runtime set_magic_quotes_runtime get_magic_quotes_gpc get_magic_quotes_runtime import_request_variables error_log serialize unserialize memory_get_usage var_dump var_export debug_zval_dump print_r highlight_file show_source highlight_string ini_get ini_get_all ini_set ini_alter ini_restore get_include_path set_include_path restore_include_path setcookie header headers_sent connection_aborted connection_status ignore_user_abort parse_ini_file is_uploaded_file move_uploaded_file intval floatval doubleval strval gettype settype is_null is_resource is_bool is_long is_float is_int is_integer is_double is_real is_numeric is_string is_array is_object is_scalar ereg ereg_replace eregi eregi_replace split spliti join sql_regcase dl pclose popen readfile rewind rmdir umask fclose feof fgetc fgets fgetss fread fopen fpassthru ftruncate fstat fseek ftell fflush fwrite fputs mkdir rename copy tempnam tmpfile file file_get_contents file_put_contents stream_select stream_context_create stream_context_set_params stream_context_set_option stream_context_get_options stream_filter_prepend stream_filter_append fgetcsv flock get_meta_tags stream_set_write_buffer set_file_buffer set_socket_blocking stream_set_blocking socket_set_blocking stream_get_meta_data stream_register_wrapper stream_wrapper_register stream_set_timeout socket_set_timeout socket_get_status realpath fnmatch fsockopen pfsockopen pack unpack get_browser crypt opendir closedir chdir getcwd rewinddir readdir dir glob fileatime filectime filegroup fileinode filemtime fileowner fileperms filesize filetype file_exists is_writable is_writeable is_readable is_executable is_file is_dir is_link stat lstat chown touch clearstatcache mail ob_start ob_flush ob_clean ob_end_flush ob_end_clean ob_get_flush ob_get_clean ob_get_length ob_get_level ob_get_status ob_get_contents ob_implicit_flush ob_list_handlers ksort krsort natsort natcasesort asort arsort sort rsort usort uasort uksort shuffle array_walk count end prev next reset current key min max in_array array_search extract compact array_fill range array_multisort array_push array_pop array_shift array_unshift array_splice array_slice array_merge array_merge_recursive array_keys array_values array_count_values array_reverse array_reduce array_pad array_flip array_change_key_case array_rand array_unique array_intersect array_intersect_assoc array_diff array_diff_assoc array_sum array_filter array_map array_chunk array_key_exists array_intersect_key array_combine array_column pos sizeof key_exists assert assert_options version_compare ftok str_rot13 aggregate session_name session_module_name session_save_path session_id session_regenerate_id session_decode session_register session_unregister session_is_registered session_encode session_start session_destroy session_unset session_set_save_handler session_cache_limiter session_cache_expire session_set_cookie_params session_get_cookie_params session_write_close preg_match preg_match_all preg_replace preg_replace_callback preg_split preg_quote preg_grep overload ctype_alnum ctype_alpha ctype_cntrl ctype_digit ctype_lower ctype_graph ctype_print ctype_punct ctype_space ctype_upper ctype_xdigit virtual apache_request_headers apache_note apache_lookup_uri apache_child_terminate apache_setenv apache_response_headers apache_get_version getallheaders mysql_connect mysql_pconnect mysql_close mysql_select_db mysql_create_db mysql_drop_db mysql_query mysql_unbuffered_query mysql_db_query mysql_list_dbs mysql_list_tables mysql_list_fields mysql_list_processes mysql_error mysql_errno mysql_affected_rows mysql_insert_id mysql_result mysql_num_rows mysql_num_fields mysql_fetch_row mysql_fetch_array mysql_fetch_assoc mysql_fetch_object mysql_data_seek mysql_fetch_lengths mysql_fetch_field mysql_field_seek mysql_free_result mysql_field_name mysql_field_table mysql_field_len mysql_field_type mysql_field_flags mysql_escape_string mysql_real_escape_string mysql_stat mysql_thread_id mysql_client_encoding mysql_get_client_info mysql_get_host_info mysql_get_proto_info mysql_get_server_info mysql_info mysql mysql_fieldname mysql_fieldtable mysql_fieldlen mysql_fieldtype mysql_fieldflags mysql_selectdb mysql_createdb mysql_dropdb mysql_freeresult mysql_numfields mysql_numrows mysql_listdbs mysql_listtables mysql_listfields mysql_db_name mysql_dbname mysql_tablename mysql_table_name pg_connect pg_pconnect pg_close pg_connection_status pg_connection_busy pg_connection_reset pg_host pg_dbname pg_port pg_tty pg_options pg_ping pg_query pg_send_query pg_cancel_query pg_fetch_result pg_fetch_row pg_fetch_assoc pg_fetch_array pg_fetch_object pg_fetch_all pg_affected_rows pg_get_result pg_result_seek pg_result_status pg_free_result pg_last_oid pg_num_rows pg_num_fields pg_field_name pg_field_num pg_field_size pg_field_type pg_field_prtlen pg_field_is_null pg_get_notify pg_get_pid pg_result_error pg_last_error pg_last_notice pg_put_line pg_end_copy pg_copy_to pg_copy_from pg_trace pg_untrace pg_lo_create pg_lo_unlink pg_lo_open pg_lo_close pg_lo_read pg_lo_write pg_lo_read_all pg_lo_import pg_lo_export pg_lo_seek pg_lo_tell pg_escape_string pg_escape_bytea pg_unescape_bytea pg_client_encoding pg_set_client_encoding pg_meta_data pg_convert pg_insert pg_update pg_delete pg_select pg_exec pg_getlastoid pg_cmdtuples pg_errormessage pg_numrows pg_numfields pg_fieldname pg_fieldsize pg_fieldtype pg_fieldnum pg_fieldprtlen pg_fieldisnull pg_freeresult pg_result pg_loreadall pg_locreate pg_lounlink pg_loopen pg_loclose pg_loread pg_lowrite pg_loimport pg_loexport http_response_code get_declared_traits getimagesizefromstring socket_import_stream stream_set_chunk_size trait_exists header_register_callback class_uses session_status session_register_shutdown echo print global static exit array empty eval isset unset die include require include_once require_once json_decode json_encode json_last_error json_last_error_msg curl_close curl_copy_handle curl_errno curl_error curl_escape curl_exec curl_file_create curl_getinfo curl_init curl_multi_add_handle curl_multi_close curl_multi_exec curl_multi_getcontent curl_multi_info_read curl_multi_init curl_multi_remove_handle curl_multi_select curl_multi_setopt curl_multi_strerror curl_pause curl_reset curl_setopt_array curl_setopt curl_share_close curl_share_init curl_share_setopt curl_strerror curl_unescape curl_version mysqli_affected_rows mysqli_autocommit mysqli_change_user mysqli_character_set_name mysqli_close mysqli_commit mysqli_connect_errno mysqli_connect_error mysqli_connect mysqli_data_seek mysqli_debug mysqli_dump_debug_info mysqli_errno mysqli_error_list mysqli_error mysqli_fetch_all mysqli_fetch_array mysqli_fetch_assoc mysqli_fetch_field_direct mysqli_fetch_field mysqli_fetch_fields mysqli_fetch_lengths mysqli_fetch_object mysqli_fetch_row mysqli_field_count mysqli_field_seek mysqli_field_tell mysqli_free_result mysqli_get_charset mysqli_get_client_info mysqli_get_client_stats mysqli_get_client_version mysqli_get_connection_stats mysqli_get_host_info mysqli_get_proto_info mysqli_get_server_info mysqli_get_server_version mysqli_info mysqli_init mysqli_insert_id mysqli_kill mysqli_more_results mysqli_multi_query mysqli_next_result mysqli_num_fields mysqli_num_rows mysqli_options mysqli_ping mysqli_prepare mysqli_query mysqli_real_connect mysqli_real_escape_string mysqli_real_query mysqli_reap_async_query mysqli_refresh mysqli_rollback mysqli_select_db mysqli_set_charset mysqli_set_local_infile_default mysqli_set_local_infile_handler mysqli_sqlstate mysqli_ssl_set mysqli_stat mysqli_stmt_init mysqli_store_result mysqli_thread_id mysqli_thread_safe mysqli_use_result mysqli_warning_count";c.registerHelper("hintWords","php",[t,r,n].join(" ").split(" ")),c.registerHelper("wordChars","php",/[\w$]/);var i={name:"clike",helperType:"php",keywords:e(t),blockKeywords:e("catch do else elseif for foreach if switch try while finally"),defKeywords:e("class function interface namespace trait"),atoms:e(r),builtin:e(n),multiLineStrings:!0,hooks:{$:function(e){return e.eatWhile(/[\w\$_]/),"variable-2"},"<":function(e,t){var r;if(r=e.match(/<<\s*/)){var n=e.eat(/['"]/);e.eatWhile(/[\w\.]/);var i=e.current().slice(r[0].length+(n?2:1));if(n&&e.eat(n),i)return(t.tokStack||(t.tokStack=[])).push(i,0),t.tokenize=s(i,"'"!=n),"string"}return!1},"#":function(e){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"},"/":function(e){if(e.eat("/")){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"}return!1},'"':function(e,t){return(t.tokStack||(t.tokStack=[])).push('"',0),t.tokenize=s('"'),"string"},"{":function(e,t){return t.tokStack&&t.tokStack.length&&t.tokStack[t.tokStack.length-1]++,!1},"}":function(e,t){return t.tokStack&&0<t.tokStack.length&&!--t.tokStack[t.tokStack.length-1]&&(t.tokenize=s(t.tokStack[t.tokStack.length-2])),!1}}};c.defineMode("php",function(e,r){var l=c.getMode(e,r&&r.htmlMode||"text/html"),s=c.getMode(e,i);return{startState:function(){var e=c.startState(l),t=r.startOpen?c.startState(s):null;return{html:e,php:t,curMode:r.startOpen?s:l,curState:r.startOpen?t:e,pending:null}},copyState:function(e){var t,r=e.html,n=c.copyState(l,r),i=e.php,o=i&&c.copyState(s,i);return t=e.curMode==l?n:o,{html:n,php:o,curMode:e.curMode,curState:t,pending:e.pending}},token:function(e,t){var r=t.curMode==s;if(e.sol()&&t.pending&&'"'!=t.pending&&"'"!=t.pending&&(t.pending=null),r)return r&&null==t.php.tokenize&&e.match("?>")?(t.curMode=l,t.curState=t.html,t.php.context.prev||(t.php=null),"meta"):s.token(e,t.curState);if(e.match(/^<\?\w*/))return t.curMode=s,t.php||(t.php=c.startState(s,l.indent(t.html,""))),t.curState=t.php,"meta";if('"'==t.pending||"'"==t.pending){for(;!e.eol()&&e.next()!=t.pending;);var n="string"}else t.pending&&e.pos<t.pending.end?(e.pos=t.pending.end,n=t.pending.style):n=l.token(e,t.curState);t.pending&&(t.pending=null);var i,o=e.current(),a=o.search(/<\?/);return-1!=a&&("string"==n&&(i=o.match(/[\'\"]$/))&&!/\?>/.test(o)?t.pending=i[0]:t.pending={end:e.pos,style:n},e.backUp(o.length-a)),n},indent:function(e,t){return e.curMode!=s&&/^\s*<\//.test(t)||e.curMode==s&&/^\?>/.test(t)?l.indent(e.html,t):e.curMode.indent(e.curState,t)},blockCommentStart:"/*",blockCommentEnd:"*/",lineComment:"//",innerMode:function(e){return{state:e.curState,mode:e.curMode}}}},"htmlmixed","clike"),c.defineMIME("application/x-httpd-php","php"),c.defineMIME("application/x-httpd-php-open",{name:"php",startOpen:!0}),c.defineMIME("text/x-php",i)}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(_){"use strict";var C={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},S={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};_.defineMode("xml",function(e,t){var a,o,l=e.indentUnit,s={},r=t.htmlMode?C:S;for(var n in r)s[n]=r[n];for(var n in t)s[n]=t[n];function c(t,r){function e(e){return(r.tokenize=e)(t,r)}var n=t.next();return"<"==n?t.eat("!")?t.eat("[")?t.match("CDATA[")?e(i("atom","]]>")):null:t.match("--")?e(i("comment","--\x3e")):t.match("DOCTYPE",!0,!0)?(t.eatWhile(/[\w\._\-]/),e(function n(i){return function(e,t){for(var r;null!=(r=e.next());){if("<"==r)return t.tokenize=n(i+1),t.tokenize(e,t);if(">"==r){if(1==i){t.tokenize=c;break}return t.tokenize=n(i-1),t.tokenize(e,t)}}return"meta"}}(1))):null:t.eat("?")?(t.eatWhile(/[\w\._\-]/),r.tokenize=i("meta","?>"),"meta"):(a=t.eat("/")?"closeTag":"openTag",r.tokenize=u,"tag bracket"):"&"==n?(t.eat("#")?t.eat("x")?t.eatWhile(/[a-fA-F\d]/)&&t.eat(";"):t.eatWhile(/[\d]/)&&t.eat(";"):t.eatWhile(/[\w\.\-:]/)&&t.eat(";"))?"atom":"error":(t.eatWhile(/[^&<]/),null)}function u(e,t){var r,n,i=e.next();if(">"==i||"/"==i&&e.eat(">"))return t.tokenize=c,a=">"==i?"endTag":"selfcloseTag","tag bracket";if("="==i)return a="equals",null;if("<"==i){t.tokenize=c,t.state=p,t.tagName=t.tagStart=null;var o=t.tokenize(e,t);return o?o+" tag error":"tag error"}return/[\'\"]/.test(i)?(t.tokenize=(r=i,(n=function(e,t){for(;!e.eol();)if(e.next()==r){t.tokenize=u;break}return"string"}).isInAttribute=!0,n),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function i(r,n){return function(e,t){for(;!e.eol();){if(e.match(n)){t.tokenize=c;break}e.next()}return r}}function d(e,t,r){this.prev=e.context,this.tagName=t,this.indent=e.indented,this.startOfLine=r,(s.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function f(e){e.context&&(e.context=e.context.prev)}function h(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!s.contextGrabbers.hasOwnProperty(r)||!s.contextGrabbers[r].hasOwnProperty(t))return;f(e)}}function p(e,t,r){return"openTag"==e?(r.tagStart=t.column(),m):"closeTag"==e?g:p}function m(e,t,r){return"word"==e?(r.tagName=t.current(),o="tag",b):s.allowMissingTagName&&"endTag"==e?(o="tag bracket",b(e,t,r)):(o="error",m)}function g(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&s.implicitlyClosed.hasOwnProperty(r.context.tagName)&&f(r),r.context&&r.context.tagName==n||!1===s.matchClosing?(o="tag",v):(o="tag error",y)}return s.allowMissingTagName&&"endTag"==e?(o="tag bracket",v(e,t,r)):(o="error",y)}function v(e,t,r){return"endTag"!=e?(o="error",v):(f(r),p)}function y(e,t,r){return o="error",v(e,0,r)}function b(e,t,r){if("word"==e)return o="attribute",w;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,i=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||s.autoSelfClosers.hasOwnProperty(n)?h(r,n):(h(r,n),r.context=new d(r,n,i==r.indented)),p}return o="error",b}function w(e,t,r){return"equals"==e?x:(s.allowMissing||(o="error"),b(e,0,r))}function x(e,t,r){return"string"==e?k:"word"==e&&s.allowUnquoted?(o="string",b):(o="error",b(e,0,r))}function k(e,t,r){return"string"==e?k:b(e,0,r)}return c.isInText=!0,{startState:function(e){var t={tokenize:c,state:p,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;a=null;var r=t.tokenize(e,t);return(r||a)&&"comment"!=r&&(o=null,t.state=t.state(a||r,e,t),o&&(r="error"==o?r+" error":o)),r},indent:function(e,t,r){var n=e.context;if(e.tokenize.isInAttribute)return e.tagStart==e.indented?e.stringStartCol+1:e.indented+l;if(n&&n.noIndent)return _.Pass;if(e.tokenize!=u&&e.tokenize!=c)return r?r.match(/^(\s*)/)[0].length:0;if(e.tagName)return!1!==s.multilineTagIndentPastTag?e.tagStart+e.tagName.length+2:e.tagStart+l*(s.multilineTagIndentFactor||1);if(s.alignCDATA&&/<!\[CDATA\[/.test(t))return 0;var i=t&&/^<(\/)?([\w_:\.-]*)/.exec(t);if(i&&i[1])for(;n;){if(n.tagName==i[2]){n=n.prev;break}if(!s.implicitlyClosed.hasOwnProperty(n.tagName))break;n=n.prev}else if(i)for(;n;){var o=s.contextGrabbers[n.tagName];if(!o||!o.hasOwnProperty(i[2]))break;n=n.prev}for(;n&&n.prev&&!n.startOfLine;)n=n.prev;return n?n.indent+l:e.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:s.htmlMode?"html":"xml",helperType:s.htmlMode?"html":"xml",skipAttribute:function(e){e.state==x&&(e.state=b)}}}),_.defineMIME("text/xml","xml"),_.defineMIME("application/xml","xml"),_.mimeModes.hasOwnProperty("text/html")||_.defineMIME("text/html",{name:"xml",htmlMode:!0})});